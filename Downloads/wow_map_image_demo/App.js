/**
 * WoW Classic Interactive Map - React Native App
 * Main Application Component
 */

import React, {useState, useEffect} from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
  Text,
  Alert,
  Platform,
} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import custom components
import WowMapViewer from './src/components/WowMapViewer';
import LanguageSelector from './src/components/LanguageSelector';
import LocationSearch from './src/components/LocationSearch';
import FilterControls from './src/components/FilterControls';
import LocationDetails from './src/components/LocationDetails';

// Import data and utilities
import {accurateLocationData} from './src/data/locations';
import {translations, getCurrentLanguage, setLanguage} from './src/utils/translations';

const App = () => {
  // State management
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [filteredLocations, setFilteredLocations] = useState(accurateLocationData);
  const [activeFilters, setActiveFilters] = useState({
    city: true,
    town: true,
    dungeon: true,
    raid: true,
    flight_path: true,
    landmark: true,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Initialize app
  useEffect(() => {
    initializeApp();
  }, []);

  // Filter locations when filters or search changes
  useEffect(() => {
    filterLocations();
  }, [activeFilters, searchQuery, currentLanguage]);

  const initializeApp = async () => {
    try {
      // Load saved language preference
      const savedLanguage = await AsyncStorage.getItem('mapLanguage');
      if (savedLanguage) {
        setCurrentLanguage(savedLanguage);
        setLanguage(savedLanguage);
      }
      
      setIsLoading(false);
    } catch (error) {
      console.error('Error initializing app:', error);
      setIsLoading(false);
    }
  };

  const filterLocations = () => {
    let filtered = accurateLocationData.filter(location => {
      // Filter by type
      if (!activeFilters[location.type]) {
        return false;
      }

      // Filter by search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const name = location.name.toLowerCase();
        const zone = location.zone.toLowerCase();
        const description = location.description.toLowerCase();
        
        return name.includes(query) || 
               zone.includes(query) || 
               description.includes(query);
      }

      return true;
    });

    setFilteredLocations(filtered);
  };

  const handleLanguageChange = async (language) => {
    try {
      setCurrentLanguage(language);
      setLanguage(language);
      await AsyncStorage.setItem('mapLanguage', language);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  };

  const handleLocationPress = (location) => {
    setSelectedLocation(location);
  };

  const handleFilterChange = (filterType, enabled) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterType]: enabled,
    }));
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleCoordinateClick = (coordinates) => {
    Alert.alert(
      'Map Coordinates',
      `X: ${Math.round(coordinates.x)}, Y: ${Math.round(coordinates.y)}`,
      [
        {text: 'Copy', onPress: () => copyToClipboard(`[${Math.round(coordinates.x)}, ${Math.round(coordinates.y)}]`)},
        {text: 'OK', style: 'default'},
      ]
    );
  };

  const copyToClipboard = (text) => {
    // Note: React Native doesn't have built-in clipboard API
    // You might want to add react-native-clipboard for this functionality
    Alert.alert('Copied', `Coordinates copied: ${text}`);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading WoW Classic Map...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaView style={styles.container}>
        <StatusBar
          barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
          backgroundColor="#1a1a1b"
        />
        
        {/* Header Controls */}
        <View style={styles.header}>
          <Text style={styles.title}>WoW Classic Map</Text>
          <LanguageSelector
            currentLanguage={currentLanguage}
            onLanguageChange={handleLanguageChange}
          />
        </View>

        {/* Search and Filters */}
        <View style={styles.controlsContainer}>
          <LocationSearch
            searchQuery={searchQuery}
            onSearchChange={handleSearchChange}
            placeholder={translations[currentLanguage]?.search || 'Search locations...'}
          />
          
          <FilterControls
            activeFilters={activeFilters}
            onFilterChange={handleFilterChange}
            currentLanguage={currentLanguage}
          />
        </View>

        {/* Map Viewer */}
        <View style={styles.mapContainer}>
          <WowMapViewer
            locations={filteredLocations}
            onLocationPress={handleLocationPress}
            onCoordinatePress={handleCoordinateClick}
            currentLanguage={currentLanguage}
          />
        </View>

        {/* Location Details Modal */}
        {selectedLocation && (
          <LocationDetails
            location={selectedLocation}
            currentLanguage={currentLanguage}
            onClose={() => setSelectedLocation(null)}
          />
        )}

        {/* Stats Footer */}
        <View style={styles.footer}>
          <Text style={styles.statsText}>
            Showing {filteredLocations.length} of {accurateLocationData.length} locations
          </Text>
        </View>
      </SafeAreaView>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1b',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1b',
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#2d2d30',
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  title: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  controlsContainer: {
    backgroundColor: '#2d2d30',
    paddingHorizontal: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  mapContainer: {
    flex: 1,
  },
  footer: {
    backgroundColor: '#2d2d30',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#404040',
  },
  statsText: {
    color: '#cccccc',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default App;
