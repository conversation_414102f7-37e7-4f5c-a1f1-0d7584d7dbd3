<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>WoW Classic Map (ImageOverlay Demo)</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
  <style>
    #map {
      width: 100%;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div id="map"></div>
  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script>
    // 假设地图原图是 8192x6144 (请根据实际图片替换)
    var mapWidth = 8192, mapHeight = 6144;
    var mapBounds = [[0,0], [mapHeight, mapWidth]];

    // 初始化地图 (简单二维坐标系)
    var map = L.map('map', {
      crs: L.CRS.Simple,
      minZoom: -2,
      maxZoom: 2
    });

    // 使用 ImageOverlay 加载整张大图
    L.imageOverlay('wowmap.png', mapBounds).addTo(map);

    // 自动适配视图
    map.fitBounds(mapBounds);

    // WoW Classic Major Locations Data
    var locationData = [
      // Alliance Cities
      {
        "name": "Stormwind City",
        "type": "city",
        "faction": "Alliance",
        "coords": [1800, 2200],
        "zone": "Elwynn Forest",
        "description": "Capital city of the Alliance"
      },
      {
        "name": "Ironforge",
        "type": "city",
        "faction": "Alliance",
        "coords": [1200, 1800],
        "zone": "Dun Morogh",
        "description": "Dwarven capital city"
      },
      {
        "name": "Darnassus",
        "type": "city",
        "faction": "Alliance",
        "coords": [500, 800],
        "zone": "Teldrassil",
        "description": "Night Elf capital city"
      },

      // Horde Cities
      {
        "name": "Orgrimmar",
        "type": "city",
        "faction": "Horde",
        "coords": [3200, 2800],
        "zone": "Durotar",
        "description": "Capital city of the Horde"
      },
      {
        "name": "Thunder Bluff",
        "type": "city",
        "faction": "Horde",
        "coords": [2800, 3200],
        "zone": "Mulgore",
        "description": "Tauren capital city"
      },
      {
        "name": "Undercity",
        "type": "city",
        "faction": "Horde",
        "coords": [1600, 1200],
        "zone": "Tirisfal Glades",
        "description": "Undead capital city"
      },

      // Neutral Cities
      {
        "name": "Booty Bay",
        "type": "town",
        "faction": "Neutral",
        "coords": [2400, 4200],
        "zone": "Stranglethorn Vale",
        "description": "Goblin trading port"
      },
      {
        "name": "Gadgetzan",
        "type": "town",
        "faction": "Neutral",
        "coords": [3600, 3800],
        "zone": "Tanaris",
        "description": "Goblin desert town"
      },

      // Major Dungeons
      {
        "name": "The Deadmines",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [1400, 2600],
        "zone": "Westfall",
        "description": "Level 15-25 dungeon"
      },
      {
        "name": "Wailing Caverns",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [3000, 3400],
        "zone": "The Barrens",
        "description": "Level 15-25 dungeon"
      },
      {
        "name": "Blackrock Depths",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [2200, 3000],
        "zone": "Blackrock Mountain",
        "description": "Level 50-60 dungeon"
      },

      // Raids
      {
        "name": "Molten Core",
        "type": "raid",
        "faction": "Neutral",
        "coords": [2200, 3000],
        "zone": "Blackrock Mountain",
        "description": "40-man raid"
      },
      {
        "name": "Onyxia's Lair",
        "type": "raid",
        "faction": "Neutral",
        "coords": [2000, 4000],
        "zone": "Dustwallow Marsh",
        "description": "40-man raid"
      }
    ];

    // Define custom icons for different location types
    var cityIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: gold; width: 20px; height: 20px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });

    var townIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: orange; width: 15px; height: 15px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [15, 15],
      iconAnchor: [7, 7]
    });

    var dungeonIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: red; width: 18px; height: 18px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [18, 18],
      iconAnchor: [9, 9]
    });

    var raidIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: purple; width: 22px; height: 22px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [22, 22],
      iconAnchor: [11, 11]
    });

    // Function to get appropriate icon based on location type
    function getLocationIcon(type) {
      switch(type) {
        case 'city': return cityIcon;
        case 'town': return townIcon;
        case 'dungeon': return dungeonIcon;
        case 'raid': return raidIcon;
        default: return cityIcon;
      }
    }

    // Add markers for all locations (注意 Leaflet 使用 [y, x])
    locationData.forEach(function(location) {
      var icon = getLocationIcon(location.type);
      var marker = L.marker([location.coords[1], location.coords[0]], {icon: icon}).addTo(map);

      marker.bindPopup(`
        <b>${location.name}</b><br>
        Type: ${location.type}<br>
        Zone: ${location.zone}<br>
        Faction: ${location.faction}<br>
        ${location.description}
      `);
    });
  </script>
</body>
</html>
