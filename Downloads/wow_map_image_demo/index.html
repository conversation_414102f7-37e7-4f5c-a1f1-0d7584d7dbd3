<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>WoW Classic Map (ImageOverlay Demo)</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
  <style>
    #map {
      width: 100%;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div id="map"></div>
  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script>
    // 假设地图原图是 8192x6144 (请根据实际图片替换)
    var mapWidth = 8192, mapHeight = 6144;
    var mapBounds = [[0,0], [mapHeight, mapWidth]];

    // 初始化地图 (简单二维坐标系)
    var map = L.map('map', {
      crs: L.CRS.Simple,
      minZoom: -2,
      maxZoom: 2
    });

    // 使用 ImageOverlay 加载整张大图
    L.imageOverlay('wowmap.png', mapBounds).addTo(map);

    // 自动适配视图
    map.fitBounds(mapBounds);

    // 示例掉落数据
    var lootData = [
      {
        "item_id": 102,
        "name": "灰色外套",
        "zone": "The Deadmines",
        "coords": [410, 305],
        "source": "Foreman",
        "drop_rate": "~12%",
        "wowhead": "https://classic.wowhead.com/item=102"
      }
    ];

    // 添加标记 (注意 Leaflet 使用 [y, x])
    lootData.forEach(function(item) {
      var marker = L.marker([item.coords[1], item.coords[0]]).addTo(map);
      marker.bindPopup(`
        <b>${item.name}</b><br>
        区域: ${item.zone}<br>
        掉落者: ${item.source}<br>
        掉落率: ${item.drop_rate}<br>
        <a href="${item.wowhead}" target="_blank">Wowhead</a>
      `);
    });
  </script>
</body>
</html>
