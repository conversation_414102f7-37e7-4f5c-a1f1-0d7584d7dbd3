<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>WoW Classic Map (ImageOverlay Demo)</title>
  <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
  <style>
    #map {
      width: 100%;
      height: 100vh;
    }
  </style>
</head>
<body>
  <!-- Language Selector -->
  <div id="language-selector" style="position: absolute; top: 10px; right: 10px; z-index: 1000; background: white; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
    <label for="language-select">Language / 语言 / Idioma:</label>
    <select id="language-select" onchange="changeLanguage(this.value)">
      <option value="en">English</option>
      <option value="zh">中文</option>
      <option value="es">Español</option>
    </select>
  </div>

  <div id="map"></div>
  <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
  <script src="translations.js"></script>
  <script src="accurate_locations.js"></script>
  <script>
    // Assuming the original map image is 8192x6144 (replace with actual image dimensions)
    var mapWidth = 8192, mapHeight = 6144;
    var mapBounds = [[0,0], [mapHeight, mapWidth]];

    // Initialize map (simple 2D coordinate system)
    var map = L.map('map', {
      crs: L.CRS.Simple,
      minZoom: -2,
      maxZoom: 2
    });

    // Use ImageOverlay to load the full map image
    L.imageOverlay('wowmap.png', mapBounds).addTo(map);

    // Auto-fit view to map bounds
    map.fitBounds(mapBounds);

    // WoW Classic Major Locations Data
    var locationData = [
      // Alliance Cities
      {
        "name": "Stormwind City",
        "type": "city",
        "faction": "Alliance",
        "coords": [1800, 2200],
        "zone": "Elwynn Forest",
        "description": "Capital city of the Alliance"
      },
      {
        "name": "Ironforge",
        "type": "city",
        "faction": "Alliance",
        "coords": [1200, 1800],
        "zone": "Dun Morogh",
        "description": "Dwarven capital city"
      },
      {
        "name": "Darnassus",
        "type": "city",
        "faction": "Alliance",
        "coords": [500, 800],
        "zone": "Teldrassil",
        "description": "Night Elf capital city"
      },

      // Horde Cities
      {
        "name": "Orgrimmar",
        "type": "city",
        "faction": "Horde",
        "coords": [3200, 2800],
        "zone": "Durotar",
        "description": "Capital city of the Horde"
      },
      {
        "name": "Thunder Bluff",
        "type": "city",
        "faction": "Horde",
        "coords": [2800, 3200],
        "zone": "Mulgore",
        "description": "Tauren capital city"
      },
      {
        "name": "Undercity",
        "type": "city",
        "faction": "Horde",
        "coords": [1600, 1200],
        "zone": "Tirisfal Glades",
        "description": "Undead capital city"
      },

      // Neutral Cities
      {
        "name": "Booty Bay",
        "type": "town",
        "faction": "Neutral",
        "coords": [2400, 4200],
        "zone": "Stranglethorn Vale",
        "description": "Goblin trading port"
      },
      {
        "name": "Gadgetzan",
        "type": "town",
        "faction": "Neutral",
        "coords": [3600, 3800],
        "zone": "Tanaris",
        "description": "Goblin desert town"
      },

      // Major Dungeons
      {
        "name": "The Deadmines",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [1400, 2600],
        "zone": "Westfall",
        "description": "Level 15-25 dungeon"
      },
      {
        "name": "Wailing Caverns",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [3000, 3400],
        "zone": "The Barrens",
        "description": "Level 15-25 dungeon"
      },
      {
        "name": "Blackrock Depths",
        "type": "dungeon",
        "faction": "Neutral",
        "coords": [2200, 3000],
        "zone": "Blackrock Mountain",
        "description": "Level 50-60 dungeon"
      },

      // Raids
      {
        "name": "Molten Core",
        "type": "raid",
        "faction": "Neutral",
        "coords": [2200, 3000],
        "zone": "Blackrock Mountain",
        "description": "40-man raid"
      },
      {
        "name": "Onyxia's Lair",
        "type": "raid",
        "faction": "Neutral",
        "coords": [2000, 4000],
        "zone": "Dustwallow Marsh",
        "description": "40-man raid"
      }
    ];

    // Define custom icons for different location types
    var cityIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: gold; width: 20px; height: 20px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });

    var townIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: orange; width: 15px; height: 15px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [15, 15],
      iconAnchor: [7, 7]
    });

    var dungeonIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: red; width: 18px; height: 18px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [18, 18],
      iconAnchor: [9, 9]
    });

    var raidIcon = L.divIcon({
      className: 'custom-div-icon',
      html: '<div style="background-color: purple; width: 22px; height: 22px; border-radius: 50%; border: 2px solid #333;"></div>',
      iconSize: [22, 22],
      iconAnchor: [11, 11]
    });

    // Function to get appropriate icon based on location type
    function getLocationIcon(type) {
      switch(type) {
        case 'city': return cityIcon;
        case 'town': return townIcon;
        case 'dungeon': return dungeonIcon;
        case 'raid': return raidIcon;
        default: return cityIcon;
      }
    }

    // Store markers globally for language updates
    var allMarkers = [];

    // Function to create popup content with translations
    function createPopupContent(location) {
      const translatedName = translateLocation(location.name, 'name');
      const translatedDescription = translateLocation(location.name, 'description');
      const translatedType = translate(location.type);
      const translatedFaction = translate(location.faction.toLowerCase());

      return `
        <b>${translatedName}</b><br>
        ${translate('type')}: ${translatedType}<br>
        ${translate('zone')}: ${location.zone}<br>
        ${translate('faction')}: ${translatedFaction}<br>
        ${translatedDescription}
      `;
    }

    // Add markers for all locations (Note: Leaflet uses [y, x] coordinate format)
    locationData.forEach(function(location) {
      var icon = getLocationIcon(location.type);
      var marker = L.marker([location.coords[1], location.coords[0]], {icon: icon}).addTo(map);

      // Store location data with marker for updates
      marker.locationData = location;
      marker.bindPopup(createPopupContent(location));

      allMarkers.push(marker);
    });

    // Function to change language
    function changeLanguage(newLang) {
      setLanguage(newLang);
      updateMapLanguage();
    }

    // Function to update all markers with new language
    function updateMapLanguage() {
      // Update page title
      document.title = translate('title');

      // Update all marker popups
      allMarkers.forEach(function(marker) {
        marker.setPopupContent(createPopupContent(marker.locationData));
      });
    }

    // Coordinate debugging tool - click map to see coordinates
    map.on('click', function(e) {
      const coords = e.latlng;
      console.log(`Clicked coordinates: [${Math.round(coords.lng)}, ${Math.round(coords.lat)}]`);
      alert(`Coordinates: [${Math.round(coords.lng)}, ${Math.round(coords.lat)}]\n(Copy this for location data)`);
    });

    // Initialize with saved language
    document.addEventListener('DOMContentLoaded', function() {
      const savedLang = getCurrentLanguage();
      document.getElementById('language-select').value = savedLang;
      updateMapLanguage();
    });
  </script>
</body>
</html>
