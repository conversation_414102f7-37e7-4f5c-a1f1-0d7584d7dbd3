{"name": "wow-classic-map-app", "version": "1.0.0", "description": "WoW Classic Interactive Map - React Native App", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "metro": "npx react-native start", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace WowClassicMap.xcworkspace -scheme WowClassicMap -configuration Release -destination generic/platform=iOS -archivePath WowClassicMap.xcarchive archive"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "react-native-maps": "^1.8.0", "react-native-image-zoom-viewer": "^3.0.1", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-svg": "^13.14.0", "react-native-vector-icons": "^10.0.0", "react-native-picker-select": "^8.1.0", "react-native-async-storage": "^1.19.3", "@react-native-async-storage/async-storage": "^1.19.3", "react-native-image-pan-zoom": "^2.1.12", "react-native-super-grid": "^4.9.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "wow", "world-of-warcraft", "classic", "map", "interactive", "gaming"], "author": "WoW Classic Map Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/wow-classic-map-app.git"}, "bugs": {"url": "https://github.com/your-username/wow-classic-map-app/issues"}, "homepage": "https://github.com/your-username/wow-classic-map-app#readme"}