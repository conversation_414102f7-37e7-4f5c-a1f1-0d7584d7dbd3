#!/bin/bash

# WoW Classic Map React Native App Setup Script

echo "🗺️  Setting up WoW Classic Map React Native App..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 16+ first."
    exit 1
fi

# Check if React Native CLI is installed
if ! command -v react-native &> /dev/null; then
    echo "📱 Installing React Native CLI..."
    npm install -g react-native-cli
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Check if we're on macOS for iOS setup
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "🍎 Setting up iOS dependencies..."
    
    # Check if CocoaPods is installed
    if ! command -v pod &> /dev/null; then
        echo "📱 Installing CocoaPods..."
        sudo gem install cocoapods
    fi
    
    # Install iOS pods
    echo "📱 Installing iOS pods..."
    cd ios && pod install && cd ..
    
    echo "✅ iOS setup complete!"
else
    echo "⚠️  iOS setup skipped (not on macOS)"
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p android/app/src/main/assets
mkdir -p ios/WowClassicMap/Images.xcassets

# Check if map image exists
if [ ! -f "assets/wowmap.png" ]; then
    echo "⚠️  Map image not found at assets/wowmap.png"
    echo "📥 Please download the high-resolution WoW Classic map:"
    echo "   https://archive.org/details/wow_classic_high_resolution_world_terrain_map_azeroth"
    echo "   Save it as assets/wowmap.png"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📱 To run the app:"
echo "   iOS:     npm run ios"
echo "   Android: npm run android"
echo ""
echo "🔧 Development commands:"
echo "   Start Metro: npm start"
echo "   Run tests:   npm test"
echo "   Lint code:   npm run lint"
echo ""
echo "📖 See README.md for detailed instructions"
