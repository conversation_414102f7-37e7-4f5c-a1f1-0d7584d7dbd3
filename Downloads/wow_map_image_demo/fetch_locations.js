// WoW Classic Location Data Fetcher
// Run this script to get more location data

// Extended location dataset with more comprehensive data
const extendedLocationData = [
  // === ALLIANCE ZONES ===
  // Elwynn Forest
  {
    "name": "Goldshire",
    "type": "town",
    "faction": "Alliance",
    "coords": [1850, 2150],
    "zone": "Elwynn Forest",
    "description": "Starting town for humans"
  },
  {
    "name": "Northshire Abbey",
    "type": "landmark",
    "faction": "Alliance", 
    "coords": [1900, 2100],
    "zone": "Elwynn Forest",
    "description": "Human starting area"
  },
  
  // Westfall
  {
    "name": "Sentinel Hill",
    "type": "town",
    "faction": "Alliance",
    "coords": [1350, 2550],
    "zone": "Westfall",
    "description": "Main town in Westfall"
  },
  
  // Redridge Mountains
  {
    "name": "Lakeshire",
    "type": "town", 
    "faction": "Alliance",
    "coords": [2100, 2400],
    "zone": "Redridge Mountains",
    "description": "Alliance town"
  },
  
  // Duskwood
  {
    "name": "Darkshire",
    "type": "town",
    "faction": "Alliance",
    "coords": [1800, 2800],
    "zone": "Duskwood", 
    "description": "Spooky Alliance town"
  },
  
  // === HORDE ZONES ===
  // The Barrens
  {
    "name": "Crossroads",
    "type": "town",
    "faction": "Horde",
    "coords": [3100, 3300],
    "zone": "The Barrens",
    "description": "Major Horde outpost"
  },
  {
    "name": "Ratchet",
    "type": "town",
    "faction": "Neutral",
    "coords": [3200, 3600],
    "zone": "The Barrens", 
    "description": "Goblin port town"
  },
  
  // Stonetalon Mountains
  {
    "name": "Sun Rock Retreat",
    "type": "town",
    "faction": "Horde",
    "coords": [2900, 3100],
    "zone": "Stonetalon Mountains",
    "description": "Horde outpost"
  },
  
  // === NEUTRAL/CONTESTED ZONES ===
  // Stranglethorn Vale
  {
    "name": "Grom'gol Base Camp",
    "type": "town",
    "faction": "Horde", 
    "coords": [2300, 4000],
    "zone": "Stranglethorn Vale",
    "description": "Horde base in STV"
  },
  {
    "name": "Rebel Camp",
    "type": "town",
    "faction": "Alliance",
    "coords": [2200, 4100],
    "zone": "Stranglethorn Vale",
    "description": "Alliance base in STV"
  },
  
  // === MORE DUNGEONS ===
  {
    "name": "Shadowfang Keep",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [1500, 1000],
    "zone": "Silverpine Forest",
    "description": "Level 20-30 dungeon"
  },
  {
    "name": "Stockade",
    "type": "dungeon", 
    "faction": "Alliance",
    "coords": [1800, 2200],
    "zone": "Stormwind City",
    "description": "Level 20-30 dungeon"
  },
  {
    "name": "Razorfen Kraul",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [3000, 3500],
    "zone": "The Barrens",
    "description": "Level 25-35 dungeon"
  },
  {
    "name": "Scarlet Monastery",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [1700, 1300],
    "zone": "Tirisfal Glades", 
    "description": "Level 30-45 dungeon complex"
  },
  {
    "name": "Uldaman",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2500, 2200],
    "zone": "Badlands",
    "description": "Level 35-45 dungeon"
  },
  {
    "name": "Zul'Farrak",
    "type": "dungeon",
    "faction": "Neutral", 
    "coords": [3700, 3900],
    "zone": "Tanaris",
    "description": "Level 45-55 dungeon"
  },
  {
    "name": "Maraudon",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2700, 3700],
    "zone": "Desolace",
    "description": "Level 45-55 dungeon"
  },
  {
    "name": "Sunken Temple",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2000, 4300],
    "zone": "Swamp of Sorrows",
    "description": "Level 50-60 dungeon"
  },
  {
    "name": "Blackrock Spire",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2200, 3000],
    "zone": "Blackrock Mountain",
    "description": "Level 55-60 dungeon"
  },
  {
    "name": "Dire Maul",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [1800, 3400],
    "zone": "Feralas", 
    "description": "Level 55-60 dungeon complex"
  },
  {
    "name": "Stratholme",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2800, 1400],
    "zone": "Eastern Plaguelands",
    "description": "Level 55-60 dungeon"
  },
  {
    "name": "Scholomance",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [2600, 1600],
    "zone": "Western Plaguelands",
    "description": "Level 55-60 dungeon"
  },
  
  // === FLIGHT PATHS ===
  {
    "name": "Menethil Harbor",
    "type": "flight_path",
    "faction": "Alliance",
    "coords": [1000, 1600],
    "zone": "Wetlands",
    "description": "Alliance flight path and port"
  },
  {
    "name": "Auberdine",
    "type": "flight_path", 
    "faction": "Alliance",
    "coords": [400, 1000],
    "zone": "Darkshore",
    "description": "Night Elf port town"
  },
  {
    "name": "Astranaar",
    "type": "flight_path",
    "faction": "Alliance",
    "coords": [1600, 2800],
    "zone": "Ashenvale",
    "description": "Night Elf town"
  }
];

// Function to merge with existing data
function mergeLocationData() {
  // This would merge with your existing locationData array
  console.log('Extended location data ready to merge:');
  console.log(JSON.stringify(extendedLocationData, null, 2));
}

// Function to fetch data from external sources (example)
async function fetchWowheadData(zoneId) {
  try {
    // Note: This is a conceptual example - actual implementation would need CORS handling
    const response = await fetch(`https://classic.wowhead.com/zone=${zoneId}`);
    const html = await response.text();
    
    // Parse HTML to extract location data
    // This would require a proper HTML parser and CORS proxy
    console.log('Fetched data for zone:', zoneId);
    
  } catch (error) {
    console.error('Error fetching data:', error);
  }
}

// Export for use in your main file
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { extendedLocationData, mergeLocationData };
}

// For browser usage
if (typeof window !== 'undefined') {
  window.extendedLocationData = extendedLocationData;
  window.mergeLocationData = mergeLocationData;
}
