<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WoW Classic Data Fetcher</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .zone-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 10px; margin: 20px 0; }
        .zone-card { border: 1px solid #ccc; padding: 10px; border-radius: 5px; }
        .zone-card.fetched { background-color: #e8f5e8; }
        .zone-card.error { background-color: #ffe8e8; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .progress { margin: 20px 0; }
        .output { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; max-height: 400px; overflow-y: auto; }
        .instructions { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ WoW Classic Data Fetcher</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li><strong>Manual Method (Recommended):</strong> Click "Open Wowhead Zone" buttons below, then use browser console to extract data</li>
                <li><strong>Automated Method:</strong> Click "Fetch All Data" (may be blocked by CORS)</li>
                <li><strong>Copy Results:</strong> Use the generated JavaScript code in your map</li>
            </ol>
            
            <h4>🔧 Browser Console Script:</h4>
            <p>Copy this script and paste it in the browser console on any Wowhead zone page:</p>
            <textarea readonly style="width: 100%; height: 100px;">
// Paste this in browser console on Wowhead zone pages
function extractWowheadData() {
    const locations = [];
    const zoneName = document.querySelector('h1.heading-size-1')?.textContent || 'Unknown Zone';
    
    // Extract from listview
    document.querySelectorAll('.listview-row').forEach(row => {
        const nameElem = row.querySelector('.q');
        const coordsText = row.textContent;
        const coordMatch = coordsText.match(/(\d+\.?\d*),\s*(\d+\.?\d*)/);
        
        if (nameElem && coordMatch) {
            const name = nameElem.textContent.trim();
            const x = parseFloat(coordMatch[1]);
            const y = parseFloat(coordMatch[2]);
            
            locations.push({
                name: name,
                type: row.dataset.type || 'unknown',
                coords: [x, y],
                zone: zoneName
            });
        }
    });
    
    console.log('Extracted locations:', locations);
    return { zone: zoneName, locations: locations };
}

// Run extraction
extractWowheadData();
            </textarea>
        </div>

        <div class="controls">
            <button onclick="fetchAllData()">🚀 Fetch All Data (Auto)</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
            <button onclick="downloadResults()">💾 Download JSON</button>
            <button onclick="generateJavaScript()">📝 Generate JavaScript</button>
        </div>

        <div class="progress">
            <div id="progress-bar" style="width: 0%; height: 20px; background: #4CAF50; border-radius: 10px;"></div>
            <div id="progress-text">Ready to fetch data...</div>
        </div>

        <h3>🌍 WoW Classic Zones</h3>
        <div class="zone-grid" id="zone-grid">
            <!-- Zones will be populated by JavaScript -->
        </div>

        <h3>📊 Results</h3>
        <div class="output" id="output">
            <p>Results will appear here...</p>
        </div>
    </div>

    <script>
        // WoW Classic zones data
        const zones = {
            12: "Elwynn Forest",
            1: "Dun Morogh",
            14: "Durotar", 
            17: "The Barrens",
            215: "Mulgore",
            85: "Tirisfal Glades",
            141: "Teldrassil",
            148: "Darkshore",
            40: "Westfall",
            44: "Redridge Mountains",
            10: "Duskwood",
            39: "Stranglethorn Vale",
            440: "Tanaris",
            405: "Desolace",
            406: "Stonetalon Mountains",
            331: "Ashenvale",
            357: "Feralas",
            490: "Un'Goro Crater",
            618: "Winterspring",
            28: "Western Plaguelands",
            139: "Eastern Plaguelands",
            51: "Searing Gorge",
            46: "Burning Steppes",
            36: "Alterac Mountains",
            45: "Arathi Highlands",
            3: "Badlands",
            4: "Blasted Lands"
        };

        let fetchedData = {};
        let currentZone = 0;
        let totalZones = Object.keys(zones).length;

        // Initialize zone grid
        function initializeZoneGrid() {
            const grid = document.getElementById('zone-grid');
            
            for (const [zoneId, zoneName] of Object.entries(zones)) {
                const card = document.createElement('div');
                card.className = 'zone-card';
                card.id = `zone-${zoneId}`;
                card.innerHTML = `
                    <h4>${zoneName}</h4>
                    <p>ID: ${zoneId}</p>
                    <button onclick="openWowheadZone(${zoneId})">Open Wowhead</button>
                    <button onclick="fetchZoneData(${zoneId})">Fetch Data</button>
                    <div class="status" id="status-${zoneId}">Ready</div>
                `;
                grid.appendChild(card);
            }
        }

        // Open Wowhead zone in new tab
        function openWowheadZone(zoneId) {
            const url = `https://classic.wowhead.com/zone=${zoneId}`;
            window.open(url, '_blank');
            updateStatus(zoneId, 'Opened in new tab - use console script');
        }

        // Fetch data for a specific zone
        async function fetchZoneData(zoneId) {
            const zoneName = zones[zoneId];
            updateStatus(zoneId, 'Fetching...');
            
            try {
                // Note: This will likely fail due to CORS
                const response = await fetch(`https://classic.wowhead.com/zone=${zoneId}`, {
                    mode: 'cors'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const html = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                
                // Extract data (simplified)
                const locations = [];
                const rows = doc.querySelectorAll('.listview-row');
                
                rows.forEach(row => {
                    const nameElem = row.querySelector('.q');
                    const coordsMatch = row.textContent.match(/(\d+\.?\d*),\s*(\d+\.?\d*)/);
                    
                    if (nameElem && coordsMatch) {
                        locations.push({
                            name: nameElem.textContent.trim(),
                            type: row.dataset.type || 'unknown',
                            coords: [parseFloat(coordsMatch[1]), parseFloat(coordsMatch[2])],
                            zone: zoneName
                        });
                    }
                });
                
                fetchedData[zoneId] = {
                    zone: zoneName,
                    locations: locations
                };
                
                updateStatus(zoneId, `Found ${locations.length} locations`);
                document.getElementById(`zone-${zoneId}`).classList.add('fetched');
                
            } catch (error) {
                updateStatus(zoneId, `Error: ${error.message}`);
                document.getElementById(`zone-${zoneId}`).classList.add('error');
                console.error(`Error fetching ${zoneName}:`, error);
            }
        }

        // Fetch all zones
        async function fetchAllData() {
            const zoneIds = Object.keys(zones);
            currentZone = 0;
            
            for (const zoneId of zoneIds) {
                await fetchZoneData(zoneId);
                currentZone++;
                updateProgress();
                
                // Wait between requests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            displayResults();
        }

        // Update status for a zone
        function updateStatus(zoneId, status) {
            const statusElem = document.getElementById(`status-${zoneId}`);
            if (statusElem) {
                statusElem.textContent = status;
            }
        }

        // Update progress bar
        function updateProgress() {
            const percentage = (currentZone / totalZones) * 100;
            document.getElementById('progress-bar').style.width = `${percentage}%`;
            document.getElementById('progress-text').textContent = 
                `Progress: ${currentZone}/${totalZones} zones (${Math.round(percentage)}%)`;
        }

        // Display results
        function displayResults() {
            const output = document.getElementById('output');
            const totalLocations = Object.values(fetchedData)
                .reduce((sum, zone) => sum + (zone.locations?.length || 0), 0);
            
            output.innerHTML = `
                <h4>📈 Fetch Summary</h4>
                <p><strong>Zones fetched:</strong> ${Object.keys(fetchedData).length}</p>
                <p><strong>Total locations:</strong> ${totalLocations}</p>
                
                <h4>📋 Raw Data</h4>
                <pre>${JSON.stringify(fetchedData, null, 2)}</pre>
            `;
        }

        // Generate JavaScript code
        function generateJavaScript() {
            const locations = [];
            
            Object.values(fetchedData).forEach(zone => {
                if (zone.locations) {
                    zone.locations.forEach(loc => {
                        // Convert coordinates (assuming 8192x6144 map)
                        const pixelX = Math.round((loc.coords[0] / 100) * 8192);
                        const pixelY = Math.round((loc.coords[1] / 100) * 6144);
                        
                        locations.push({
                            name: loc.name,
                            type: loc.type === 'npc' ? 'landmark' : loc.type,
                            faction: 'Neutral',
                            coords: [pixelX, pixelY],
                            zone: loc.zone,
                            description: `${loc.type} in ${loc.zone}`
                        });
                    });
                }
            });
            
            const jsCode = `// Generated WoW Classic location data
const generatedLocationData = ${JSON.stringify(locations, null, 2)};

// Merge with existing data
if (typeof locationData !== 'undefined') {
    locationData = locationData.concat(generatedLocationData);
} else {
    var locationData = generatedLocationData;
}

console.log('Loaded', generatedLocationData.length, 'generated locations');`;
            
            const output = document.getElementById('output');
            output.innerHTML = `
                <h4>🔧 Generated JavaScript Code</h4>
                <p>Copy this code and add it to your map HTML file:</p>
                <textarea style="width: 100%; height: 300px;">${jsCode}</textarea>
            `;
        }

        // Download results as JSON
        function downloadResults() {
            const dataStr = JSON.stringify(fetchedData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = 'wow_classic_data.json';
            link.click();
            
            URL.revokeObjectURL(url);
        }

        // Clear results
        function clearResults() {
            fetchedData = {};
            currentZone = 0;
            document.getElementById('output').innerHTML = '<p>Results cleared...</p>';
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-text').textContent = 'Ready to fetch data...';
            
            // Reset zone cards
            document.querySelectorAll('.zone-card').forEach(card => {
                card.classList.remove('fetched', 'error');
                const status = card.querySelector('.status');
                if (status) status.textContent = 'Ready';
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeZoneGrid);
    </script>
</body>
</html>
