import * as React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
/**
 * @deprecated `gestureHandlerRootHOC` is deprecated and will be removed in the future version of Gesture Handler.
 * Use `GestureHandlerRootView` directly instead.
 */
export default function gestureHandlerRootHOC<P extends object>(Component: React.ComponentType<P>, containerStyles?: StyleProp<ViewStyle>): React.ComponentType<P>;
//# sourceMappingURL=gestureHandlerRootHOC.d.ts.map