{"version": 3, "names": ["React", "StyleSheet", "maybeInitializeFabric", "GestureHandlerRootViewContext", "GestureHandlerRootViewNativeComponent", "jsx", "_jsx", "GestureHandlerRootView", "style", "rest", "Provider", "value", "children", "styles", "container", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerRootView.android.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAAoBC,UAAU,QAAQ,cAAc;AACpD,SAASC,qBAAqB,QAAQ,SAAS;AAC/C,OAAOC,6BAA6B,MAAM,kCAAkC;AAC5E,OAAOC,qCAAqC,MAAM,kDAAkD;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAKrG,eAAe,SAASC,sBAAsBA,CAAC;EAC7CC,KAAK;EACL,GAAGC;AACwB,CAAC,EAAE;EAC9B;EACA;EACA;EACAP,qBAAqB,CAAC,CAAC;EAEvB,oBACEI,IAAA,CAACH,6BAA6B,CAACO,QAAQ;IAACC,KAAK;IAAAC,QAAA,eAC3CN,IAAA,CAACF,qCAAqC;MACpCI,KAAK,EAAEA,KAAK,IAAIK,MAAM,CAACC,SAAU;MAAA,GAC7BL;IAAI,CACT;EAAC,CACoC,CAAC;AAE7C;AAEA,MAAMI,MAAM,GAAGZ,UAAU,CAACc,MAAM,CAAC;EAC/BD,SAAS,EAAE;IAAEE,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}