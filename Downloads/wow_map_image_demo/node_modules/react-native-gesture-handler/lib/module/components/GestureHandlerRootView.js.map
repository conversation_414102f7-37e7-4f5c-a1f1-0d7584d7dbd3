{"version": 3, "names": ["React", "View", "StyleSheet", "maybeInitializeFabric", "GestureHandlerRootViewContext", "jsx", "_jsx", "GestureHandlerRootView", "style", "rest", "Provider", "value", "children", "styles", "container", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/GestureHandlerRootView.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,IAAI,EAAaC,UAAU,QAAQ,cAAc;AAC1D,SAASC,qBAAqB,QAAQ,SAAS;AAC/C,OAAOC,6BAA6B,MAAM,kCAAkC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAK7E,eAAe,SAASC,sBAAsBA,CAAC;EAC7CC,KAAK;EACL,GAAGC;AACwB,CAAC,EAAE;EAC9B;EACA;EACA;EACAN,qBAAqB,CAAC,CAAC;EAEvB,oBACEG,IAAA,CAACF,6BAA6B,CAACM,QAAQ;IAACC,KAAK;IAAAC,QAAA,eAC3CN,IAAA,CAACL,IAAI;MAACO,KAAK,EAAEA,KAAK,IAAIK,MAAM,CAACC,SAAU;MAAA,GAAKL;IAAI,CAAG;EAAC,CACd,CAAC;AAE7C;AAEA,MAAMI,MAAM,GAAGX,UAAU,CAACa,MAAM,CAAC;EAC/BD,SAAS,EAAE;IAAEE,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}