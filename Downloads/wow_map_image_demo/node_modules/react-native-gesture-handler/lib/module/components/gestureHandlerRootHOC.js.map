{"version": 3, "names": ["React", "StyleSheet", "hoistNonReactStatics", "GestureHandlerRootView", "jsx", "_jsx", "gestureHandlerRootHOC", "Component", "containerStyles", "Wrapper", "props", "style", "styles", "container", "children", "displayName", "name", "create", "flex"], "sourceRoot": "../../../src", "sources": ["components/gestureHandlerRootHOC.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAA8B,cAAc;AAC/D,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,0BAA0B;;AAE7D;AACA;AACA;AACA;AAHA,SAAAC,GAAA,IAAAC,IAAA;AAIA,eAAe,SAASC,qBAAqBA,CAC3CC,SAAiC,EACjCC,eAAsC,EACd;EACxB,SAASC,OAAOA,CAACC,KAAQ,EAAE;IACzB,oBACEL,IAAA,CAACF,sBAAsB;MAACQ,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAEL,eAAe,CAAE;MAAAM,QAAA,eACjET,IAAA,CAACE,SAAS;QAAA,GAAKG;MAAK,CAAG;IAAC,CACF,CAAC;EAE7B;EAEAD,OAAO,CAACM,WAAW,GAAG,yBACpBR,SAAS,CAACQ,WAAW,IAAIR,SAAS,CAACS,IAAI,GACtC;;EAEH;EACAd,oBAAoB,CAACO,OAAO,EAAEF,SAAS,CAAC;EAExC,OAAOE,OAAO;AAChB;AAEA,MAAMG,MAAM,GAAGX,UAAU,CAACgB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IAAEK,IAAI,EAAE;EAAE;AACvB,CAAC,CAAC", "ignoreList": []}