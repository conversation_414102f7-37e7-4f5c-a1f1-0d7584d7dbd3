{"version": 3, "names": ["Gesture<PERSON>andler", "TEST_MAX_IF_NOT_NAN", "DiscreteGestureHandler", "isDiscrete", "shouldEnableGestureOnSetup", "shouldFailUnderCustomCriteria", "x", "y", "deltaX", "deltaY", "maxDeltaX", "maxDeltaY", "maxDistSq", "shouldCancelWhenOutside", "isPointInView", "Math", "abs", "transformNativeEvent", "center", "rect", "view", "getBoundingClientRect", "absoluteX", "absoluteY", "left", "top", "isGestureEnabledForEvent", "minPointers", "maxPointers", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "isGestureRunning", "failed", "success"], "sourceRoot": "../../../src", "sources": ["web_hammer/DiscreteGestureHandler.ts"], "mappings": ";;AAAA;AACA;AACA,OAAOA,cAAc,MAAM,kBAAkB;AAC7C,SAASC,mBAAmB,QAAQ,SAAS;AAE7C,MAAeC,sBAAsB,SAASF,cAAc,CAAC;EAC3D,IAAIG,UAAUA,CAAA,EAAG;IACf,OAAO,IAAI;EACb;EAEA,IAAIC,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,IAAI;EACb;EAEAC,6BAA6BA,CAC3B;IAAEC,CAAC;IAAEC,CAAC;IAAEC,MAAM;IAAEC;EAAY,CAAC,EAC7B;IAAEC,SAAS;IAAEC,SAAS;IAAEC,SAAS;IAAEC;EAA6B,CAAC,EACjE;IACA,IAAIA,uBAAuB,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACC,aAAa,CAAC;QAAER,CAAC;QAAEC;MAAE,CAAC,CAAC,EAAE;QACjC,OAAO,IAAI;MACb;IACF;IACA,OACEN,mBAAmB,CAACc,IAAI,CAACC,GAAG,CAACR,MAAM,CAAC,EAAEE,SAAS,CAAC,IAChDT,mBAAmB,CAACc,IAAI,CAACC,GAAG,CAACP,MAAM,CAAC,EAAEE,SAAS,CAAC,IAChDV,mBAAmB,CACjBc,IAAI,CAACC,GAAG,CAACP,MAAM,GAAGA,MAAM,GAAGD,MAAM,GAAGA,MAAM,CAAC,EAC3CI,SACF,CAAC;EAEL;EAEAK,oBAAoBA,CAAC;IAAEC,MAAM,EAAE;MAAEZ,CAAC;MAAEC;IAAE;EAAO,CAAC,EAAE;IAC9C;IACA,MAAMY,IAAI,GAAG,IAAI,CAACC,IAAI,CAAEC,qBAAqB,CAAC,CAAC;IAE/C,OAAO;MACLC,SAAS,EAAEhB,CAAC;MACZiB,SAAS,EAAEhB,CAAC;MACZD,CAAC,EAAEA,CAAC,GAAGa,IAAI,CAACK,IAAI;MAChBjB,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACM;IACd,CAAC;EACH;EAEAC,wBAAwBA,CACtB;IACEC,WAAW;IACXC,WAAW;IACXlB,SAAS;IACTC,SAAS;IACTC,SAAS;IACTC;EACG,CAAC,EACNgB,WAAgB,EAChB;IAAED,WAAW,EAAEE,aAAa;IAAEZ,MAAM;IAAEV,MAAM;IAAEC;EAAY,CAAC,EAC3D;IACA,MAAMsB,iBAAiB,GACrBD,aAAa,IAAIH,WAAW,IAAIG,aAAa,IAAIF,WAAW;IAE9D,IACE,IAAI,CAACvB,6BAA6B,CAChC;MAAE,GAAGa,MAAM;MAAEV,MAAM;MAAEC;IAAO,CAAC,EAC7B;MACEC,SAAS;MACTC,SAAS;MACTC,SAAS;MACTC;IACF,CACF,CAAC;IACD;IACA;IACC,CAACkB,iBAAiB,IAAI,IAAI,CAACC,gBAAiB,EAC7C;MACA,OAAO;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzB;IAEA,OAAO;MAAEC,OAAO,EAAEH;IAAkB,CAAC;EACvC;AACF;AAEA,eAAe7B,sBAAsB", "ignoreList": []}