{"version": 3, "names": ["Gesture<PERSON>andler", "PixelRatio", "DraggingGestureHandler", "shouldEnableGestureOnSetup", "transformNativeEvent", "deltaX", "deltaY", "velocityX", "velocityY", "center", "x", "y", "rect", "view", "getBoundingClientRect", "ratio", "get", "translationX", "__initialX", "translationY", "__initialY", "absoluteX", "absoluteY", "left", "top"], "sourceRoot": "../../../src", "sources": ["web_hammer/DraggingGestureHandler.ts"], "mappings": ";;AAAA;AACA;AACA,OAAOA,cAAc,MAA0B,kBAAkB;AACjE,SAASC,UAAU,QAAQ,cAAc;AAEzC,MAAeC,sBAAsB,SAASF,cAAc,CAAC;EAC3D,IAAIG,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,IAAI;EACb;EAEAC,oBAAoBA,CAAC;IACnBC,MAAM;IACNC,MAAM;IACNC,SAAS;IACTC,SAAS;IACTC,MAAM,EAAE;MAAEC,CAAC;MAAEC;IAAE;EACD,CAAC,EAAE;IACjB;IACA,MAAMC,IAAI,GAAG,IAAI,CAACC,IAAI,CAAEC,qBAAqB,CAAC,CAAC;IAC/C,MAAMC,KAAK,GAAGd,UAAU,CAACe,GAAG,CAAC,CAAC;IAC9B,OAAO;MACLC,YAAY,EAAEZ,MAAM,IAAI,IAAI,CAACa,UAAU,IAAI,CAAC,CAAC;MAC7CC,YAAY,EAAEb,MAAM,IAAI,IAAI,CAACc,UAAU,IAAI,CAAC,CAAC;MAC7CC,SAAS,EAAEX,CAAC;MACZY,SAAS,EAAEX,CAAC;MACZJ,SAAS,EAAEA,SAAS,GAAGQ,KAAK;MAC5BP,SAAS,EAAEA,SAAS,GAAGO,KAAK;MAC5BL,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAACW,IAAI;MAChBZ,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACY;IACd,CAAC;EACH;AACF;AAEA,eAAetB,sBAAsB", "ignoreList": []}