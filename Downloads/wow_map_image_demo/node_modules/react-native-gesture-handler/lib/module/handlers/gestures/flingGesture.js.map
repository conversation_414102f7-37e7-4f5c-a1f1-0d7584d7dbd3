{"version": 3, "names": ["BaseGesture", "FlingGesture", "config", "constructor", "handler<PERSON>ame", "numberOfPointers", "pointers", "direction"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/flingGesture.ts"], "mappings": ";;AAAA,SAASA,WAAW,QAA2B,WAAW;AAI1D,OAAO,MAAMC,YAAY,SAASD,WAAW,CAAkC;EACtEE,MAAM,GAA2C,CAAC,CAAC;EAE1DC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,qBAAqB;EAC1C;;EAEA;AACF;AACA;AACA;EACEC,gBAAgBA,CAACC,QAAgB,EAAE;IACjC,IAAI,CAACJ,MAAM,CAACG,gBAAgB,GAAGC,QAAQ;IACvC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,SAASA,CAACA,SAAiB,EAAE;IAC3B,IAAI,CAACL,MAAM,CAACK,SAAS,GAAGA,SAAS;IACjC,OAAO,IAAI;EACb;AACF", "ignoreList": []}