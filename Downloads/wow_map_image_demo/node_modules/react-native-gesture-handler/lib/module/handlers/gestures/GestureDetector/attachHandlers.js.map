{"version": 3, "names": ["registerHandler", "RNGestureHandlerModule", "filterConfig", "scheduleFlushOperations", "ActionType", "Platform", "ghQueueMicrotask", "extractGestureRelations", "checkGestureCallbacksForWorklets", "ALLOWED_PROPS", "MountRegistry", "attachHandlers", "preparedGesture", "gestureConfig", "gestures<PERSON>oAtta<PERSON>", "viewTag", "webEventHandlersRef", "initialize", "isMounted", "prepare", "handler", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "testId", "updateGestureHandler", "gesture", "actionType", "shouldUseReanimated", "REANIMATED_WORKLET", "JS_FUNCTION_NEW_API", "OS", "attachGestureHandler", "JS_FUNCTION_OLD_API", "gestureWillMount", "attachedGestures", "animatedHandlers", "isAnimatedGesture", "g", "value", "filter", "map", "handlers"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/attachHandlers.ts"], "mappings": ";;AAEA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,aAAa;AAEnE,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,QAAQ,QAAQ,cAAc;AAEvC,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SACEC,uBAAuB,EACvBC,gCAAgC,EAChCC,aAAa,QACR,SAAS;AAChB,SAASC,aAAa,QAAQ,wBAAwB;AAUtD,OAAO,SAASC,cAAcA,CAAC;EAC7BC,eAAe;EACfC,aAAa;EACbC,gBAAgB;EAChBC,OAAO;EACPC;AACoB,CAAC,EAAE;EACvBH,aAAa,CAACI,UAAU,CAAC,CAAC;;EAE1B;EACA;EACAX,gBAAgB,CAAC,MAAM;IACrB,IAAI,CAACM,eAAe,CAACM,SAAS,EAAE;MAC9B;IACF;IACAL,aAAa,CAACM,OAAO,CAAC,CAAC;EACzB,CAAC,CAAC;EAEF,KAAK,MAAMC,OAAO,IAAIN,gBAAgB,EAAE;IACtCN,gCAAgC,CAACY,OAAO,CAAC;IACzCnB,sBAAsB,CAACoB,oBAAoB,CACzCD,OAAO,CAACE,WAAW,EACnBF,OAAO,CAACG,UAAU,EAClBrB,YAAY,CAACkB,OAAO,CAACI,MAAM,EAAEf,aAAa,CAC5C,CAAC;IAEDT,eAAe,CAACoB,OAAO,CAACG,UAAU,EAAEH,OAAO,EAAEA,OAAO,CAACI,MAAM,CAACC,MAAM,CAAC;EACrE;;EAEA;EACA;EACAnB,gBAAgB,CAAC,MAAM;IACrB,IAAI,CAACM,eAAe,CAACM,SAAS,EAAE;MAC9B;IACF;IACA,KAAK,MAAME,OAAO,IAAIN,gBAAgB,EAAE;MACtCb,sBAAsB,CAACyB,oBAAoB,CACzCN,OAAO,CAACG,UAAU,EAClBrB,YAAY,CACVkB,OAAO,CAACI,MAAM,EACdf,aAAa,EACbF,uBAAuB,CAACa,OAAO,CACjC,CACF,CAAC;IACH;IAEAjB,uBAAuB,CAAC,CAAC;EAC3B,CAAC,CAAC;EAEF,KAAK,MAAMwB,OAAO,IAAIb,gBAAgB,EAAE;IACtC,MAAMc,UAAU,GAAGD,OAAO,CAACE,mBAAmB,GAC1CzB,UAAU,CAAC0B,kBAAkB,GAC7B1B,UAAU,CAAC2B,mBAAmB;IAElC,IAAI1B,QAAQ,CAAC2B,EAAE,KAAK,KAAK,EAAE;MAEvB/B,sBAAsB,CAACgC,oBAAoB,CAE3CN,OAAO,CAACJ,UAAU,EAClBR,OAAO,EACPX,UAAU,CAAC8B,mBAAmB;MAAE;MAChClB,mBACF,CAAC;IACH,CAAC,MAAM;MACLf,sBAAsB,CAACgC,oBAAoB,CACzCN,OAAO,CAACJ,UAAU,EAClBR,OAAO,EACPa,UACF,CAAC;IACH;IAEAlB,aAAa,CAACyB,gBAAgB,CAACR,OAAO,CAAC;EACzC;EAEAf,eAAe,CAACwB,gBAAgB,GAAGtB,gBAAgB;EAEnD,IAAIF,eAAe,CAACyB,gBAAgB,EAAE;IACpC,MAAMC,iBAAiB,GAAIC,CAAc,IAAKA,CAAC,CAACV,mBAAmB;IAEnEjB,eAAe,CAACyB,gBAAgB,CAACG,KAAK,GAAG1B,gBAAgB,CACtD2B,MAAM,CAACH,iBAAiB,CAAC,CACzBI,GAAG,CAAEH,CAAC,IAAKA,CAAC,CAACI,QAAQ,CAErB;EACL;AACF", "ignoreList": []}