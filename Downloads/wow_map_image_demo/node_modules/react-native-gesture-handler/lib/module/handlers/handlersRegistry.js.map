{"version": 3, "names": ["isTestEnv", "handlerIDToTag", "gestures", "Map", "oldHandlers", "testIDs", "registerHandler", "handlerTag", "handler", "testID", "set", "registerOldGestureHandler", "unregisterOldGestureHandler", "delete", "unregister<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "get", "findOldGestureHandler", "findHandlerByTestID", "undefined"], "sourceRoot": "../../../src", "sources": ["handlers/handlersRegistry.ts"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,UAAU;AAIpC,OAAO,MAAMC,cAAsC,GAAG,CAAC,CAAC;AACxD,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAsB,CAAC;AAC/C,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAkC,CAAC;AAC9D,MAAME,OAAO,GAAG,IAAIF,GAAG,CAAiB,CAAC;AAEzC,OAAO,SAASG,eAAeA,CAC7BC,UAAkB,EAClBC,OAAoB,EACpBC,MAAe,EACf;EACAP,QAAQ,CAACQ,GAAG,CAACH,UAAU,EAAEC,OAAO,CAAC;EACjC,IAAIR,SAAS,CAAC,CAAC,IAAIS,MAAM,EAAE;IACzBJ,OAAO,CAACK,GAAG,CAACD,MAAM,EAAEF,UAAU,CAAC;EACjC;AACF;AAEA,OAAO,SAASI,yBAAyBA,CACvCJ,UAAkB,EAClBC,OAAgC,EAChC;EACAJ,WAAW,CAACM,GAAG,CAACH,UAAU,EAAEC,OAAO,CAAC;AACtC;AAEA,OAAO,SAASI,2BAA2BA,CAACL,UAAkB,EAAE;EAC9DH,WAAW,CAACS,MAAM,CAACN,UAAU,CAAC;AAChC;AAEA,OAAO,SAASO,iBAAiBA,CAACP,UAAkB,EAAEE,MAAe,EAAE;EACrEP,QAAQ,CAACW,MAAM,CAACN,UAAU,CAAC;EAC3B,IAAIP,SAAS,CAAC,CAAC,IAAIS,MAAM,EAAE;IACzBJ,OAAO,CAACQ,MAAM,CAACJ,MAAM,CAAC;EACxB;AACF;AAEA,OAAO,SAASM,WAAWA,CAACR,UAAkB,EAAE;EAC9C,OAAOL,QAAQ,CAACc,GAAG,CAACT,UAAU,CAAC;AACjC;AAEA,OAAO,SAASU,qBAAqBA,CAACV,UAAkB,EAAE;EACxD,OAAOH,WAAW,CAACY,GAAG,CAACT,UAAU,CAAC;AACpC;AAEA,OAAO,SAASW,mBAAmBA,CAACT,MAAc,EAAE;EAClD,MAAMF,UAAU,GAAGF,OAAO,CAACW,GAAG,CAACP,MAAM,CAAC;EACtC,IAAIF,UAAU,KAAKY,SAAS,EAAE;IAC5B,OAAOJ,WAAW,CAACR,UAAU,CAAC,IAAI,IAAI;EACxC;EACA,OAAO,IAAI;AACb", "ignoreList": []}