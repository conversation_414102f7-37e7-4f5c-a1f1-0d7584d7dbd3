{"version": 3, "names": ["commonProps", "componentInteractionProps", "baseGestureHandlerProps", "baseGestureHandlerWithDetectorProps", "MouseB<PERSON>on"], "sourceRoot": "../../../src", "sources": ["handlers/gestureHandlerCommon.ts"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAQA,MAAMA,WAAW,GAAG,CAClB,IAAI,EACJ,SAAS,EACT,yBAAyB,EACzB,SAAS,EACT,sBAAsB,EACtB,YAAY,EACZ,cAAc,EACd,aAAa,EACb,mBAAmB,EACnB,aAAa,CACL;AAEV,MAAMC,yBAAyB,GAAG,CAChC,SAAS,EACT,sBAAsB,EACtB,gBAAgB,CACR;AAEV,OAAO,MAAMC,uBAAuB,GAAG,CACrC,GAAGF,WAAW,EACd,GAAGC,yBAAyB,EAC5B,SAAS,EACT,UAAU,EACV,aAAa,EACb,aAAa,EACb,SAAS,EACT,gBAAgB,EAChB,sBAAsB,CACd;AAEV,OAAO,MAAME,mCAAmC,GAAG,CACjD,GAAGH,WAAW,EACd,kBAAkB,EAClB,kBAAkB,CACnB;AAkED,WAAYI,WAAW,0BAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAXA,WAAW,CAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;;AA0BvB;;AA+CA;AACA", "ignoreList": []}