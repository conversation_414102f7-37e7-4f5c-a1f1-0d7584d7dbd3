{"version": 3, "names": ["React", "tagMessage", "PlatformConstants", "createHandler", "baseGestureHandlerProps", "forceTouchGestureHandlerProps", "ForceTouchFallback", "Component", "forceTouchAvailable", "componentDidMount", "console", "warn", "render", "props", "children", "forceTouchHandlerName", "ForceTouchGestureHandler", "name", "allowedProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/ForceTouchGestureHandler.ts"], "mappings": ";;AAAA,OAAOA,KAAK,MAA6B,OAAO;AAChD,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAEEC,uBAAuB,QAClB,wBAAwB;AAG/B,OAAO,MAAMC,6BAA6B,GAAG,CAC3C,UAAU,EACV,UAAU,EACV,sBAAsB,CACd;;AAEV;AACA,MAAMC,kBAAkB,SAASN,KAAK,CAACO,SAAS,CAA6B;EAC3E,OAAOC,mBAAmB,GAAG,KAAK;EAClCC,iBAAiBA,CAAA,EAAG;IAClBC,OAAO,CAACC,IAAI,CACVV,UAAU,CACR,8NACF,CACF,CAAC;EACH;EACAW,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAACC,QAAQ;EAC5B;AACF;;AAuBA;AACA;AACA;;AAKA;AACA;AACA;;AAKA,OAAO,MAAMC,qBAAqB,GAAG,0BAA0B;;AAE/D;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAGd,iBAAiB,EAAEM,mBAAmB,GAC1EL,aAAa,CAGX;EACAc,IAAI,EAAEF,qBAAqB;EAC3BG,YAAY,EAAE,CACZ,GAAGd,uBAAuB,EAC1B,GAAGC,6BAA6B,CACxB;EACVc,MAAM,EAAE,CAAC;AACX,CAAC,CAAC,GACFb,kBAAkB;AAErBU,wBAAwB,CAA8BR,mBAAmB,GACxEN,iBAAiB,EAAEM,mBAAmB,IAAI,KAAK", "ignoreList": []}