{"version": 3, "names": ["_createHandler", "_interopRequireDefault", "require", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "e", "__esModule", "default", "flingGestureHandlerProps", "exports", "flingHandlerName", "FlingGestureHandler", "createHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "sourceRoot": "../../../src", "sources": ["handlers/FlingGestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,cAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AAGgC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEzB,MAAMG,wBAAwB,GAAAC,OAAA,CAAAD,wBAAA,GAAG,CACtC,kBAAkB,EAClB,WAAW,CACH;;AAyBV;AACA;AACA;;AAKO,MAAME,gBAAgB,GAAAD,OAAA,CAAAC,gBAAA,GAAG,qBAAqB;;AAErD;AACA;AACA;;AAGA;AACA;AACA;AACA;AACO,MAAMC,mBAAmB,GAAAF,OAAA,CAAAE,mBAAA,GAAG,IAAAC,sBAAa,EAG9C;EACAC,IAAI,EAAEH,gBAAgB;EACtBI,YAAY,EAAE,CACZ,GAAGC,6CAAuB,EAC1B,GAAGP,wBAAwB,CACnB;EACVQ,MAAM,EAAE,CAAC;AACX,CAAC,CAAC", "ignoreList": []}