{"version": 3, "names": ["_utils", "require", "handlerIDToTag", "exports", "gestures", "Map", "oldHandlers", "testIDs", "registerHandler", "handlerTag", "handler", "testID", "set", "isTestEnv", "registerOldGestureHandler", "unregisterOldGestureHandler", "delete", "unregister<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "get", "findOldGestureHandler", "findHandlerByTestID", "undefined"], "sourceRoot": "../../../src", "sources": ["handlers/handlersRegistry.ts"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAIO,MAAMC,cAAsC,GAAAC,OAAA,CAAAD,cAAA,GAAG,CAAC,CAAC;AACxD,MAAME,QAAQ,GAAG,IAAIC,GAAG,CAAsB,CAAC;AAC/C,MAAMC,WAAW,GAAG,IAAID,GAAG,CAAkC,CAAC;AAC9D,MAAME,OAAO,GAAG,IAAIF,GAAG,CAAiB,CAAC;AAElC,SAASG,eAAeA,CAC7BC,UAAkB,EAClBC,OAAoB,EACpBC,MAAe,EACf;EACAP,QAAQ,CAACQ,GAAG,CAACH,UAAU,EAAEC,OAAO,CAAC;EACjC,IAAI,IAAAG,gBAAS,EAAC,CAAC,IAAIF,MAAM,EAAE;IACzBJ,OAAO,CAACK,GAAG,CAACD,MAAM,EAAEF,UAAU,CAAC;EACjC;AACF;AAEO,SAASK,yBAAyBA,CACvCL,UAAkB,EAClBC,OAAgC,EAChC;EACAJ,WAAW,CAACM,GAAG,CAACH,UAAU,EAAEC,OAAO,CAAC;AACtC;AAEO,SAASK,2BAA2BA,CAACN,UAAkB,EAAE;EAC9DH,WAAW,CAACU,MAAM,CAACP,UAAU,CAAC;AAChC;AAEO,SAASQ,iBAAiBA,CAACR,UAAkB,EAAEE,MAAe,EAAE;EACrEP,QAAQ,CAACY,MAAM,CAACP,UAAU,CAAC;EAC3B,IAAI,IAAAI,gBAAS,EAAC,CAAC,IAAIF,MAAM,EAAE;IACzBJ,OAAO,CAACS,MAAM,CAACL,MAAM,CAAC;EACxB;AACF;AAEO,SAASO,WAAWA,CAACT,UAAkB,EAAE;EAC9C,OAAOL,QAAQ,CAACe,GAAG,CAACV,UAAU,CAAC;AACjC;AAEO,SAASW,qBAAqBA,CAACX,UAAkB,EAAE;EACxD,OAAOH,WAAW,CAACa,GAAG,CAACV,UAAU,CAAC;AACpC;AAEO,SAASY,mBAAmBA,CAACV,MAAc,EAAE;EAClD,MAAMF,UAAU,GAAGF,OAAO,CAACY,GAAG,CAACR,MAAM,CAAC;EACtC,IAAIF,UAAU,KAAKa,SAAS,EAAE;IAC5B,OAAOJ,WAAW,CAACT,UAAU,CAAC,IAAI,IAAI;EACxC;EACA,OAAO,IAAI;AACb", "ignoreList": []}