{"version": 3, "names": ["_reanimated<PERSON><PERSON>per", "require", "_State", "_utils", "warningMessage", "tagMessage", "REANIMATED_AVAILABLE", "Reanimated", "useSharedValue", "undefined", "setGestureState", "create", "handlerTag", "begin", "State", "BEGAN", "console", "warn", "activate", "ACTIVE", "fail", "FAILED", "end", "END", "GestureStateManager", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureStateManager.ts"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AASA,MAAMG,cAAc,GAAG,IAAAC,iBAAU,EAC/B,kFACF,CAAC;;AAED;AACA;AACA,MAAMC,oBAAoB,GAAGC,6BAAU,EAAEC,cAAc,KAAKC,SAAS;AACrE,MAAMC,eAAe,GAAGH,6BAAU,EAAEG,eAAe;AAEnD,SAASC,MAAMA,CAACC,UAAkB,EAA2B;EAC3D,SAAS;;EACT,OAAO;IACLC,KAAK,EAAEA,CAAA,KAAM;MACX,SAAS;;MACT,IAAIP,oBAAoB,EAAE;QACxB;QACA;QACAI,eAAe,CAAEE,UAAU,EAAEE,YAAK,CAACC,KAAK,CAAC;MAC3C,CAAC,MAAM;QACLC,OAAO,CAACC,IAAI,CAACb,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDc,QAAQ,EAAEA,CAAA,KAAM;MACd,SAAS;;MACT,IAAIZ,oBAAoB,EAAE;QACxB;QACA;QACAI,eAAe,CAAEE,UAAU,EAAEE,YAAK,CAACK,MAAM,CAAC;MAC5C,CAAC,MAAM;QACLH,OAAO,CAACC,IAAI,CAACb,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDgB,IAAI,EAAEA,CAAA,KAAM;MACV,SAAS;;MACT,IAAId,oBAAoB,EAAE;QACxB;QACA;QACAI,eAAe,CAAEE,UAAU,EAAEE,YAAK,CAACO,MAAM,CAAC;MAC5C,CAAC,MAAM;QACLL,OAAO,CAACC,IAAI,CAACb,cAAc,CAAC;MAC9B;IACF,CAAC;IAEDkB,GAAG,EAAEA,CAAA,KAAM;MACT,SAAS;;MACT,IAAIhB,oBAAoB,EAAE;QACxB;QACA;QACAI,eAAe,CAAEE,UAAU,EAAEE,YAAK,CAACS,GAAG,CAAC;MACzC,CAAC,MAAM;QACLP,OAAO,CAACC,IAAI,CAACb,cAAc,CAAC;MAC9B;IACF;EACF,CAAC;AACH;AAEO,MAAMoB,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,GAAG;EACjCb;AACF,CAAC", "ignoreList": []}