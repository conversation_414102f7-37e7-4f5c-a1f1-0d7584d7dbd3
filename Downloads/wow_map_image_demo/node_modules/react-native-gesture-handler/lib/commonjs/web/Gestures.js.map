{"version": 3, "names": ["_PanGestureHandler", "_interopRequireDefault", "require", "_TapGestureHandler", "_LongPressGestureHandler", "_PinchGestureHandler", "_RotationGestureHandler", "_FlingGestureHandler", "_NativeViewGestureHandler", "_ManualGestureHandler", "_HoverGestureHandler", "_NativeViewGestureHandler2", "_PanGestureHandler2", "_TapGestureHandler2", "_LongPressGestureHandler2", "_PinchGestureHandler2", "_RotationGestureHandler2", "_FlingGestureHandler2", "e", "__esModule", "default", "Gestures", "exports", "NativeViewGestureHandler", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "ManualGestureHandler", "HoverGestureHandler", "HammerGestures", "HammerNativeViewGestureHandler", "HammerPanGestureHandler", "HammerTapGestureHandler", "HammerLongPressGestureHandler", "HammerPinchGestureHandler", "HammerRotationGestureHandler", "HammerFlingGestureHandler"], "sourceRoot": "../../../src", "sources": ["web/Gestures.ts"], "mappings": ";;;;;;AACA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,wBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,oBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,uBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,oBAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,yBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,qBAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,oBAAA,GAAAT,sBAAA,CAAAC,OAAA;AAGA,IAAAS,0BAAA,GAAAV,sBAAA,CAAAC,OAAA;AACA,IAAAU,mBAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,mBAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,yBAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,qBAAA,GAAAd,sBAAA,CAAAC,OAAA;AACA,IAAAc,wBAAA,GAAAf,sBAAA,CAAAC,OAAA;AACA,IAAAe,qBAAA,GAAAhB,sBAAA,CAAAC,OAAA;AAA0E,SAAAD,uBAAAiB,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAlB1E;;AAWA;;AASO,MAAMG,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG;EACtBE,wBAAwB,EAAxBA,iCAAwB;EACxBC,iBAAiB,EAAjBA,0BAAiB;EACjBC,iBAAiB,EAAjBA,0BAAiB;EACjBC,uBAAuB,EAAvBA,gCAAuB;EACvBC,mBAAmB,EAAnBA,4BAAmB;EACnBC,sBAAsB,EAAtBA,+BAAsB;EACtBC,mBAAmB,EAAnBA,4BAAmB;EACnBC,oBAAoB,EAApBA,6BAAoB;EACpBC,mBAAmB,EAAnBA;AACF,CAAC;AAEM,MAAMC,cAAc,GAAAV,OAAA,CAAAU,cAAA,GAAG;EAC5BT,wBAAwB,EAAEU,kCAA8B;EACxDT,iBAAiB,EAAEU,2BAAuB;EAC1CT,iBAAiB,EAAEU,2BAAuB;EAC1CT,uBAAuB,EAAEU,iCAA6B;EACtDT,mBAAmB,EAAEU,6BAAyB;EAC9CT,sBAAsB,EAAEU,gCAA4B;EACpDT,mBAAmB,EAAEU;AACvB,CAAC", "ignoreList": []}