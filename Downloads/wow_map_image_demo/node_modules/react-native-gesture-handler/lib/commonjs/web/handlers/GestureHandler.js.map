{"version": 3, "names": ["_State", "require", "_interfaces", "_GestureHandlerOrchestrator", "_interopRequireDefault", "_InteractionManager", "_PointerTracker", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_PointerType", "e", "__esModule", "default", "Gesture<PERSON>andler", "lastSentState", "_state", "State", "UNDETERMINED", "_shouldCancelWhenOutside", "hasCustomActivationCriteria", "_enabled", "_config", "enabled", "_tracker", "PointerTracker", "_activationIndex", "_awaiting", "_active", "_shouldResetProgress", "_pointerType", "PointerType", "MOUSE", "constructor", "delegate", "_delegate", "init", "viewRef", "propsRef", "state", "attachEventManager", "manager", "setOnPointerDown", "onPointerDown", "bind", "setOnPointerAdd", "onPointerAdd", "setOnPointerUp", "onPointerUp", "setOnPointerRemove", "onPointerRemove", "setOnPointerMove", "onPointerMove", "setOnPointerEnter", "onPointerEnter", "setOnPointerLeave", "onPointerLeave", "setOnPointerCancel", "onPointerCancel", "setOnPointerOutOfBounds", "onPointerOutOfBounds", "setOnPointerMoveOver", "onPointerMoveOver", "setOnPointerMoveOut", "onPointerMoveOut", "setOnWheel", "onWheel", "registerListeners", "onCancel", "onReset", "resetProgress", "reset", "tracker", "resetTracker", "moveToState", "newState", "sendIfDisabled", "oldState", "trackedPointersCount", "config", "needsPointerData", "isFinished", "cancelTouches", "GestureHandlerOrchestrator", "instance", "onHandlerStateChange", "onStateChange", "_newState", "_oldState", "begin", "checkHitSlop", "BEGAN", "fail", "ACTIVE", "onFail", "FAILED", "cancel", "CANCELLED", "activate", "force", "manualActivation", "onActivate", "end", "onEnd", "END", "getShouldResetProgress", "shouldResetProgress", "setShouldResetProgress", "value", "shouldWaitForHandlerFailure", "handler", "InteractionManager", "shouldRequireToWaitForFailure", "shouldRequireHandlerToWaitForFailure", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "shouldHandlerBeCancelledBy", "event", "recordHandlerIfNotPresent", "pointerType", "TOUCH", "cancelMouseAndPenGestures", "tryToSendTouchEvent", "tryToSendMoveEvent", "shouldCancelWhenOutside", "_event", "out", "active", "sendEvent", "sendTouchEvent", "onGestureHandlerEvent", "current", "touchEvent", "transformTouchEvent", "invokeNullableMethod", "onGestureHandlerStateChange", "resultEvent", "transformEventData", "nativeEvent", "undefined", "numberOfPointers", "pointerInside", "isPointerInBounds", "getAbsoluteCoordsAverage", "transformNativeEvent", "handlerTag", "target", "timeStamp", "Date", "now", "rect", "measure<PERSON>iew", "all", "changed", "trackerData", "trackedPointers", "size", "has", "pointerId", "for<PERSON>ach", "element", "key", "id", "getMappedTouchEventId", "push", "x", "abosoluteCoords", "pageX", "y", "pageY", "absoluteX", "absoluteY", "eventType", "EventTypes", "CANCEL", "TouchEventType", "DOWN", "ADDITIONAL_POINTER_DOWN", "UP", "ADDITIONAL_POINTER_UP", "MOVE", "numberOfTouches", "length", "changedTouches", "allTouches", "cancelEvent", "lastCoords", "lastRelativeCoords", "getRelativeCoordsAverage", "updateGestureConfig", "props", "onEnabledChange", "validateHitSlops", "removeHandlerFromOrchestrator", "checkCustomActivationCriteria", "criterias", "indexOf", "hitSlop", "left", "right", "width", "Error", "height", "top", "bottom", "horizontal", "vertical", "getLastAbsoluteCoords", "offsetX", "offsetY", "isButtonInConfig", "mouseButton", "MouseB<PERSON>on", "LEFT", "resetConfig", "onDestroy", "destroy", "_handlerTag", "awaiting", "activationIndex", "getTrackedPointersID", "trackedPointersIDs", "exports", "method", "__<PERSON><PERSON><PERSON><PERSON>", "arg<PERSON><PERSON><PERSON>", "__nodeConfig", "Array", "isArray", "index", "entries", "nativeValue", "setValue"], "sourceRoot": "../../../../src", "sources": ["web/handlers/GestureHandler.ts"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAWA,IAAAE,2BAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,mBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,eAAA,GAAAF,sBAAA,CAAAH,OAAA;AAEA,IAAAM,qBAAA,GAAAN,OAAA;AACA,IAAAO,YAAA,GAAAP,OAAA;AAAgD,SAAAG,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAlBhD;;AAqBe,MAAeG,cAAc,CAA4B;EAC9DC,aAAa,GAAiB,IAAI;EAElCC,MAAM,GAAUC,YAAK,CAACC,YAAY;EAElCC,wBAAwB,GAAG,KAAK;EAC9BC,2BAA2B,GAAG,KAAK;EACrCC,QAAQ,GAAG,KAAK;EAKhBC,OAAO,GAAW;IAAEC,OAAO,EAAE;EAAM,CAAC;EAEpCC,QAAQ,GAAmB,IAAIC,uBAAc,CAAC,CAAC;;EAEvD;EACQC,gBAAgB,GAAG,CAAC;EAEpBC,SAAS,GAAG,KAAK;EACjBC,OAAO,GAAG,KAAK;EAEfC,oBAAoB,GAAG,KAAK;EAC5BC,YAAY,GAAgBC,wBAAW,CAACC,KAAK;EAI9CC,WAAWA,CAChBC,QAA0D,EAC1D;IACA,IAAI,CAACC,SAAS,GAAGD,QAAQ;EAC3B;;EAEA;EACA;EACA;;EAEUE,IAAIA,CAACC,OAAe,EAAEC,QAAkC,EAAE;IAClE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACE,KAAK,GAAGtB,YAAK,CAACC,YAAY;IAE/B,IAAI,CAACgB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;EACnC;EAEOG,kBAAkBA,CAACC,OAA8B,EAAQ;IAC9DA,OAAO,CAACC,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvDH,OAAO,CAACI,eAAe,CAAC,IAAI,CAACC,YAAY,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACrDH,OAAO,CAACM,cAAc,CAAC,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IACnDH,OAAO,CAACQ,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAACN,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3DH,OAAO,CAACU,gBAAgB,CAAC,IAAI,CAACC,aAAa,CAACR,IAAI,CAAC,IAAI,CAAC,CAAC;IACvDH,OAAO,CAACY,iBAAiB,CAAC,IAAI,CAACC,cAAc,CAACV,IAAI,CAAC,IAAI,CAAC,CAAC;IACzDH,OAAO,CAACc,iBAAiB,CAAC,IAAI,CAACC,cAAc,CAACZ,IAAI,CAAC,IAAI,CAAC,CAAC;IACzDH,OAAO,CAACgB,kBAAkB,CAAC,IAAI,CAACC,eAAe,CAACd,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3DH,OAAO,CAACkB,uBAAuB,CAAC,IAAI,CAACC,oBAAoB,CAAChB,IAAI,CAAC,IAAI,CAAC,CAAC;IACrEH,OAAO,CAACoB,oBAAoB,CAAC,IAAI,CAACC,iBAAiB,CAAClB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/DH,OAAO,CAACsB,mBAAmB,CAAC,IAAI,CAACC,gBAAgB,CAACpB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7DH,OAAO,CAACwB,UAAU,CAAC,IAAI,CAACC,OAAO,CAACtB,IAAI,CAAC,IAAI,CAAC,CAAC;IAE3CH,OAAO,CAAC0B,iBAAiB,CAAC,CAAC;EAC7B;;EAEA;EACA;EACA;;EAEUC,QAAQA,CAAA,EAAS,CAAC;EAClBC,OAAOA,CAAA,EAAS,CAAC;EACjBC,aAAaA,CAAA,EAAS,CAAC;EAE1BC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC;IAC3B,IAAI,CAACJ,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACpC,QAAQ,CAACqC,KAAK,CAAC,CAAC;IACrB,IAAI,CAAChC,KAAK,GAAGtB,YAAK,CAACC,YAAY;EACjC;;EAEA;EACA;EACA;;EAEOwD,WAAWA,CAACC,QAAe,EAAEC,cAAwB,EAAE;IAC5D,IAAI,IAAI,CAACrC,KAAK,KAAKoC,QAAQ,EAAE;MAC3B;IACF;IAEA,MAAME,QAAQ,GAAG,IAAI,CAACtC,KAAK;IAC3B,IAAI,CAACA,KAAK,GAAGoC,QAAQ;IAErB,IACE,IAAI,CAACH,OAAO,CAACM,oBAAoB,GAAG,CAAC,IACrC,IAAI,CAACC,MAAM,CAACC,gBAAgB,IAC5B,IAAI,CAACC,UAAU,CAAC,CAAC,EACjB;MACA,IAAI,CAACC,aAAa,CAAC,CAAC;IACtB;IAEAC,mCAA0B,CAACC,QAAQ,CAACC,oBAAoB,CACtD,IAAI,EACJV,QAAQ,EACRE,QAAQ,EACRD,cACF,CAAC;IAED,IAAI,CAACU,aAAa,CAACX,QAAQ,EAAEE,QAAQ,CAAC;IAEtC,IAAI,CAAC,IAAI,CAACtD,OAAO,IAAI,IAAI,CAAC0D,UAAU,CAAC,CAAC,EAAE;MACtC,IAAI,CAAC1C,KAAK,GAAGtB,YAAK,CAACC,YAAY;IACjC;EACF;EAEUoE,aAAaA,CAACC,SAAgB,EAAEC,SAAgB,EAAQ,CAAC;EAE5DC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACxB;IACF;IAEA,IAAI,IAAI,CAACnD,KAAK,KAAKtB,YAAK,CAACC,YAAY,EAAE;MACrC,IAAI,CAACwD,WAAW,CAACzD,YAAK,CAAC0E,KAAK,CAAC;IAC/B;EACF;;EAEA;AACF;AACA;EACSC,IAAIA,CAAChB,cAAwB,EAAQ;IAC1C,IAAI,IAAI,CAACrC,KAAK,KAAKtB,YAAK,CAAC4E,MAAM,IAAI,IAAI,CAACtD,KAAK,KAAKtB,YAAK,CAAC0E,KAAK,EAAE;MAC7D;MACA;MACA,IAAI,CAACzD,QAAQ,CAAC4D,MAAM,CAAC,CAAC;MAEtB,IAAI,CAACpB,WAAW,CAACzD,YAAK,CAAC8E,MAAM,EAAEnB,cAAc,CAAC;IAChD;IAEA,IAAI,CAACN,aAAa,CAAC,CAAC;EACtB;;EAEA;AACF;AACA;EACS0B,MAAMA,CAACpB,cAAwB,EAAQ;IAC5C,IACE,IAAI,CAACrC,KAAK,KAAKtB,YAAK,CAAC4E,MAAM,IAC3B,IAAI,CAACtD,KAAK,KAAKtB,YAAK,CAACC,YAAY,IACjC,IAAI,CAACqB,KAAK,KAAKtB,YAAK,CAAC0E,KAAK,EAC1B;MACA,IAAI,CAACvB,QAAQ,CAAC,CAAC;;MAEf;MACA,IAAI,CAAClC,QAAQ,CAACkC,QAAQ,CAAC,CAAC;MAExB,IAAI,CAACM,WAAW,CAACzD,YAAK,CAACgF,SAAS,EAAErB,cAAc,CAAC;IACnD;EACF;EAEOsB,QAAQA,CAACC,KAAK,GAAG,KAAK,EAAE;IAC7B,IACE,CAAC,IAAI,CAACpB,MAAM,CAACqB,gBAAgB,KAAK,IAAI,IAAID,KAAK,MAC9C,IAAI,CAAC5D,KAAK,KAAKtB,YAAK,CAACC,YAAY,IAAI,IAAI,CAACqB,KAAK,KAAKtB,YAAK,CAAC0E,KAAK,CAAC,EACjE;MACA,IAAI,CAACzD,QAAQ,CAACmE,UAAU,CAAC,CAAC;MAC1B,IAAI,CAAC3B,WAAW,CAACzD,YAAK,CAAC4E,MAAM,CAAC;IAChC;EACF;EAEOS,GAAGA,CAAA,EAAG;IACX,IAAI,IAAI,CAAC/D,KAAK,KAAKtB,YAAK,CAAC0E,KAAK,IAAI,IAAI,CAACpD,KAAK,KAAKtB,YAAK,CAAC4E,MAAM,EAAE;MAC7D;MACA,IAAI,CAAC3D,QAAQ,CAACqE,KAAK,CAAC,CAAC;MAErB,IAAI,CAAC7B,WAAW,CAACzD,YAAK,CAACuF,GAAG,CAAC;IAC7B;IAEA,IAAI,CAAClC,aAAa,CAAC,CAAC;EACtB;;EAEA;EACA;EACA;;EAEOmC,sBAAsBA,CAAA,EAAY;IACvC,OAAO,IAAI,CAACC,mBAAmB;EACjC;EACOC,sBAAsBA,CAACC,KAAc,EAAQ;IAClD,IAAI,CAACF,mBAAmB,GAAGE,KAAK;EAClC;EAEOC,2BAA2BA,CAACC,OAAwB,EAAW;IACpE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOC,2BAAkB,CAAC3B,QAAQ,CAACyB,2BAA2B,CAC5D,IAAI,EACJC,OACF,CAAC;EACH;EAEOE,6BAA6BA,CAACF,OAAwB,EAAW;IACtE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOC,2BAAkB,CAAC3B,QAAQ,CAAC6B,oCAAoC,CACrE,IAAI,EACJH,OACF,CAAC;EACH;EAEOI,6BAA6BA,CAACJ,OAAwB,EAAW;IACtE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,OAAOC,2BAAkB,CAAC3B,QAAQ,CAAC8B,6BAA6B,CAC9D,IAAI,EACJJ,OACF,CAAC;EACH;EAEOK,wBAAwBA,CAACL,OAAwB,EAAW;IACjE,IAAIA,OAAO,KAAK,IAAI,EAAE;MACpB,OAAO,KAAK;IACd;IAEA,OAAOC,2BAAkB,CAAC3B,QAAQ,CAACgC,0BAA0B,CAC3D,IAAI,EACJN,OACF,CAAC;EACH;;EAEA;EACA;EACA;;EAEUnE,aAAaA,CAAC0E,KAAmB,EAAQ;IACjDlC,mCAA0B,CAACC,QAAQ,CAACkC,yBAAyB,CAAC,IAAI,CAAC;IACnE,IAAI,CAACC,WAAW,GAAGF,KAAK,CAACE,WAAW;IAEpC,IAAI,IAAI,CAACA,WAAW,KAAKxF,wBAAW,CAACyF,KAAK,EAAE;MAC1CrC,mCAA0B,CAACC,QAAQ,CAACqC,yBAAyB,CAAC,IAAI,CAAC;IACrE;;IAEA;EACF;EACA;EACU3E,YAAYA,CAACuE,KAAmB,EAAQ;IAChD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACUrE,WAAWA,CAACqE,KAAmB,EAAQ;IAC/C,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACA;EACUnE,eAAeA,CAACmE,KAAmB,EAAQ;IACnD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACUjE,aAAaA,CAACiE,KAAmB,EAAQ;IACjD,IAAI,CAACM,kBAAkB,CAAC,KAAK,EAAEN,KAAK,CAAC;EACvC;EACU7D,cAAcA,CAAC6D,KAAmB,EAAQ;IAClD,IAAI,IAAI,CAACO,uBAAuB,EAAE;MAChC,QAAQ,IAAI,CAACrF,KAAK;QAChB,KAAKtB,YAAK,CAAC4E,MAAM;UACf,IAAI,CAACG,MAAM,CAAC,CAAC;UACb;QACF,KAAK/E,YAAK,CAAC0E,KAAK;UACd,IAAI,CAACC,IAAI,CAAC,CAAC;UACX;MACJ;MACA;IACF;IAEA,IAAI,CAAC8B,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACU/D,cAAcA,CAAC+D,KAAmB,EAAQ;IAClD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;EACjC;EACU3D,eAAeA,CAAC2D,KAAmB,EAAQ;IACnD,IAAI,CAACK,mBAAmB,CAACL,KAAK,CAAC;IAE/B,IAAI,CAACrB,MAAM,CAAC,CAAC;IACb,IAAI,CAACzB,KAAK,CAAC,CAAC;EACd;EACUX,oBAAoBA,CAACyD,KAAmB,EAAQ;IACxD,IAAI,CAACM,kBAAkB,CAAC,IAAI,EAAEN,KAAK,CAAC;EACtC;EACUvD,iBAAiBA,CAAC+D,MAAoB,EAAQ;IACtD;EAAA;EAEQ7D,gBAAgBA,CAAC6D,MAAoB,EAAQ;IACrD;EAAA;EAEQ3D,OAAOA,CAAC2D,MAAoB,EAAQ;IAC5C;EAAA;EAEQF,kBAAkBA,CAACG,GAAY,EAAET,KAAmB,EAAQ;IACpE,IAAKS,GAAG,IAAI,IAAI,CAACF,uBAAuB,IAAK,CAAC,IAAI,CAACrG,OAAO,EAAE;MAC1D;IACF;IAEA,IAAI,IAAI,CAACwG,MAAM,EAAE;MACf,IAAI,CAACC,SAAS,CAAC,IAAI,CAACzF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC;IACxC;IAEA,IAAI,CAACmF,mBAAmB,CAACL,KAAK,CAAC;EACjC;EAEUK,mBAAmBA,CAACL,KAAmB,EAAQ;IACvD,IAAI,IAAI,CAACtC,MAAM,CAACC,gBAAgB,EAAE;MAChC,IAAI,CAACiD,cAAc,CAACZ,KAAK,CAAC;IAC5B;EACF;EAEOY,cAAcA,CAACZ,KAAmB,EAAQ;IAC/C,IAAI,CAAC,IAAI,CAAC9F,OAAO,EAAE;MACjB;IACF;IAEA,MAAM;MAAE2G;IAAgC,CAAC,GAAG,IAAI,CAAC5F,QAAQ,CACtD6F,OAAmB;IAEtB,MAAMC,UAAwC,GAC5C,IAAI,CAACC,mBAAmB,CAAChB,KAAK,CAAC;IAEjC,IAAIe,UAAU,EAAE;MACdE,oBAAoB,CAACJ,qBAAqB,EAAEE,UAAU,CAAC;IACzD;EACF;;EAEA;EACA;EACA;;EAEOJ,SAAS,GAAGA,CAACrD,QAAe,EAAEE,QAAe,KAAW;IAC7D,MAAM;MAAEqD,qBAAqB;MAAEK;IAAsC,CAAC,GACpE,IAAI,CAACjG,QAAQ,CAAC6F,OAAmB;IAEnC,MAAMK,WAAwB,GAAG,IAAI,CAACC,kBAAkB,CACtD9D,QAAQ,EACRE,QACF,CAAC;;IAED;IACA;IACA;IACA;;IAEA,IAAI,IAAI,CAAC9D,aAAa,KAAK4D,QAAQ,EAAE;MACnC,IAAI,CAAC5D,aAAa,GAAG4D,QAAQ;MAC7B2D,oBAAoB,CAACC,2BAA2B,EAAEC,WAAW,CAAC;IAChE;IACA,IAAI,IAAI,CAACjG,KAAK,KAAKtB,YAAK,CAAC4E,MAAM,EAAE;MAC/B2C,WAAW,CAACE,WAAW,CAAC7D,QAAQ,GAAG8D,SAAS;MAC5CL,oBAAoB,CAACJ,qBAAqB,EAAEM,WAAW,CAAC;IAC1D;EACF,CAAC;EAEOC,kBAAkBA,CAAC9D,QAAe,EAAEE,QAAe,EAAe;IACxE,OAAO;MACL6D,WAAW,EAAE;QACXE,gBAAgB,EAAE,IAAI,CAACpE,OAAO,CAACM,oBAAoB;QACnDvC,KAAK,EAAEoC,QAAQ;QACfkE,aAAa,EAAE,IAAI,CAAC3G,QAAQ,CAAC4G,iBAAiB,CAC5C,IAAI,CAACtE,OAAO,CAACuE,wBAAwB,CAAC,CACxC,CAAC;QACD,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;QAC9BC,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3BC,MAAM,EAAE,IAAI,CAAC7G,OAAO;QACpBwC,QAAQ,EAAEF,QAAQ,KAAKE,QAAQ,GAAGA,QAAQ,GAAG8D,SAAS;QACtDpB,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEQhB,mBAAmBA,CACzBhB,KAAmB,EACW;IAC9B,MAAMiC,IAAI,GAAG,IAAI,CAACpH,QAAQ,CAACqH,WAAW,CAAC,CAAC;IAExC,MAAMC,GAAkB,GAAG,EAAE;IAC7B,MAAMC,OAAsB,GAAG,EAAE;IAEjC,MAAMC,WAAW,GAAG,IAAI,CAAClF,OAAO,CAACmF,eAAe;;IAEhD;IACA;IACA;IACA;IACA,IAAID,WAAW,CAACE,IAAI,KAAK,CAAC,IAAI,CAACF,WAAW,CAACG,GAAG,CAACxC,KAAK,CAACyC,SAAS,CAAC,EAAE;MAC/D;IACF;IAEAJ,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;MAClE,MAAMC,EAAU,GAAG,IAAI,CAAC1F,OAAO,CAAC2F,qBAAqB,CAACF,GAAG,CAAC;MAE1DT,GAAG,CAACY,IAAI,CAAC;QACPF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA;IACA,IAAInD,KAAK,CAACuD,SAAS,KAAKC,sBAAU,CAACC,MAAM,EAAE;MACzCrB,OAAO,CAACW,IAAI,CAAC;QACXF,EAAE,EAAE,IAAI,CAAC1F,OAAO,CAAC2F,qBAAqB,CAAC9C,KAAK,CAACyC,SAAS,CAAC;QACvDO,CAAC,EAAEhD,KAAK,CAACgD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACvBC,CAAC,EAAEnD,KAAK,CAACmD,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACvBC,SAAS,EAAErD,KAAK,CAACgD,CAAC;QAClBM,SAAS,EAAEtD,KAAK,CAACmD;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLd,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;QAClE,MAAMC,EAAU,GAAG,IAAI,CAAC1F,OAAO,CAAC2F,qBAAqB,CAACF,GAAG,CAAC;QAE1DR,OAAO,CAACW,IAAI,CAAC;UACXF,EAAE,EAAEA,EAAE;UACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;UACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;UACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;UACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;QACrC,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,IAAII,SAAyB,GAAGG,0BAAc,CAAC7J,YAAY;IAE3D,QAAQmG,KAAK,CAACuD,SAAS;MACrB,KAAKC,sBAAU,CAACG,IAAI;MACpB,KAAKH,sBAAU,CAACI,uBAAuB;QACrCL,SAAS,GAAGG,0BAAc,CAACC,IAAI;QAC/B;MACF,KAAKH,sBAAU,CAACK,EAAE;MAClB,KAAKL,sBAAU,CAACM,qBAAqB;QACnCP,SAAS,GAAGG,0BAAc,CAACG,EAAE;QAC7B;MACF,KAAKL,sBAAU,CAACO,IAAI;QAClBR,SAAS,GAAGG,0BAAc,CAACK,IAAI;QAC/B;MACF,KAAKP,sBAAU,CAACC,MAAM;QACpBF,SAAS,GAAGG,0BAAc,CAAC9E,SAAS;QACpC;IACJ;;IAEA;IACA;IACA;IACA,IAAIoF,eAAuB,GAAG7B,GAAG,CAAC8B,MAAM;IAExC,IACEjE,KAAK,CAACuD,SAAS,KAAKC,sBAAU,CAACK,EAAE,IACjC7D,KAAK,CAACuD,SAAS,KAAKC,sBAAU,CAACM,qBAAqB,EACpD;MACA,EAAEE,eAAe;IACnB;IAEA,OAAO;MACL3C,WAAW,EAAE;QACXO,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B1G,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBqI,SAAS,EAAEA,SAAS;QACpBW,cAAc,EAAE9B,OAAO;QACvB+B,UAAU,EAAEhC,GAAG;QACf6B,eAAe,EAAEA,eAAe;QAChC9D,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;EACH;EAEQnE,aAAaA,CAAA,EAAS;IAC5B,MAAMoE,IAAI,GAAG,IAAI,CAACpH,QAAQ,CAACqH,WAAW,CAAC,CAAC;IAExC,MAAMC,GAAkB,GAAG,EAAE;IAC7B,MAAMC,OAAsB,GAAG,EAAE;IAEjC,MAAMC,WAAW,GAAG,IAAI,CAAClF,OAAO,CAACmF,eAAe;IAEhD,IAAID,WAAW,CAACE,IAAI,KAAK,CAAC,EAAE;MAC1B;IACF;IAEAF,WAAW,CAACK,OAAO,CAAC,CAACC,OAAuB,EAAEC,GAAW,KAAW;MAClE,MAAMC,EAAU,GAAG,IAAI,CAAC1F,OAAO,CAAC2F,qBAAqB,CAACF,GAAG,CAAC;MAE1DT,GAAG,CAACY,IAAI,CAAC;QACPF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;MAEFf,OAAO,CAACW,IAAI,CAAC;QACXF,EAAE,EAAEA,EAAE;QACNG,CAAC,EAAEL,OAAO,CAACM,eAAe,CAACD,CAAC,GAAGf,IAAI,CAACiB,KAAK;QACzCC,CAAC,EAAER,OAAO,CAACM,eAAe,CAACE,CAAC,GAAGlB,IAAI,CAACmB,KAAK;QACzCC,SAAS,EAAEV,OAAO,CAACM,eAAe,CAACD,CAAC;QACpCM,SAAS,EAAEX,OAAO,CAACM,eAAe,CAACE;MACrC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMiB,WAA6B,GAAG;MACpC/C,WAAW,EAAE;QACXO,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B1G,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBqI,SAAS,EAAEG,0BAAc,CAAC9E,SAAS;QACnCsF,cAAc,EAAE9B,OAAO;QACvB+B,UAAU,EAAEhC,GAAG;QACf6B,eAAe,EAAE7B,GAAG,CAAC8B,MAAM;QAC3B/D,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;MACD4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC;IACtB,CAAC;IAED,MAAM;MAAEnB;IAAgC,CAAC,GAAG,IAAI,CAAC5F,QAAQ,CACtD6F,OAAmB;IAEtBG,oBAAoB,CAACJ,qBAAqB,EAAEuD,WAAW,CAAC;EAC1D;EAEUzC,oBAAoBA,CAAA,EAA4B;IACxD;IACA,MAAM0C,UAAU,GAAG,IAAI,CAAClH,OAAO,CAACuE,wBAAwB,CAAC,CAAC;IAC1D,MAAM4C,kBAAkB,GAAG,IAAI,CAACnH,OAAO,CAACoH,wBAAwB,CAAC,CAAC;IAElE,OAAO;MACLvB,CAAC,EAAEsB,kBAAkB,CAACtB,CAAC;MACvBG,CAAC,EAAEmB,kBAAkB,CAACnB,CAAC;MACvBE,SAAS,EAAEgB,UAAU,CAACrB,CAAC;MACvBM,SAAS,EAAEe,UAAU,CAAClB;IACxB,CAAC;EACH;;EAEA;EACA;EACA;;EAEOqB,mBAAmBA,CAAC;IAAEtK,OAAO,GAAG,IAAI;IAAE,GAAGuK;EAAc,CAAC,EAAQ;IACrE,IAAI,CAACxK,OAAO,GAAG;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGuK;IAAM,CAAC;IAE7C,IAAI,IAAI,CAACvK,OAAO,KAAKA,OAAO,EAAE;MAC5B,IAAI,CAACW,QAAQ,CAAC6J,eAAe,CAACxK,OAAO,CAAC;IACxC;IAEA,IAAI,CAACA,OAAO,GAAGA,OAAO;IAEtB,IAAI,IAAI,CAACwD,MAAM,CAAC6C,uBAAuB,KAAKe,SAAS,EAAE;MACrD,IAAI,CAACf,uBAAuB,GAAG,IAAI,CAAC7C,MAAM,CAAC6C,uBAAuB;IACpE;IAEA,IAAI,CAACoE,gBAAgB,CAAC,CAAC;IAEvB,IAAI,IAAI,CAACzK,OAAO,EAAE;MAChB;IACF;IAEA,QAAQ,IAAI,CAACgB,KAAK;MAChB,KAAKtB,YAAK,CAAC4E,MAAM;QACf,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC;QACf;MACF,KAAK3E,YAAK,CAACC,YAAY;QACrBiE,mCAA0B,CAACC,QAAQ,CAAC6G,6BAA6B,CAAC,IAAI,CAAC;QACvE;MACF;QACE,IAAI,CAACjG,MAAM,CAAC,IAAI,CAAC;QACjB;IACJ;EACF;EAEUkG,6BAA6BA,CAACC,SAAmB,EAAQ;IACjE,KAAK,MAAMlC,GAAG,IAAI,IAAI,CAAClF,MAAM,EAAE;MAC7B,IAAIoH,SAAS,CAACC,OAAO,CAACnC,GAAG,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAI,CAAC7I,2BAA2B,GAAG,IAAI;MACzC;IACF;EACF;EAEQ4K,gBAAgBA,CAAA,EAAS;IAC/B,IAAI,CAAC,IAAI,CAACjH,MAAM,CAACsH,OAAO,EAAE;MACxB;IACF;IAEA,IACE,IAAI,CAACtH,MAAM,CAACsH,OAAO,CAACC,IAAI,KAAK3D,SAAS,IACtC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACE,KAAK,KAAK5D,SAAS,IACvC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACG,KAAK,KAAK7D,SAAS,EACvC;MACA,MAAM,IAAI8D,KAAK,CACb,qEACF,CAAC;IACH;IAEA,IACE,IAAI,CAAC1H,MAAM,CAACsH,OAAO,CAACG,KAAK,KAAK7D,SAAS,IACvC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACC,IAAI,KAAK3D,SAAS,IACtC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACE,KAAK,KAAK5D,SAAS,EACvC;MACA,MAAM,IAAI8D,KAAK,CACb,8EACF,CAAC;IACH;IAEA,IACE,IAAI,CAAC1H,MAAM,CAACsH,OAAO,CAACK,MAAM,KAAK/D,SAAS,IACxC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACM,GAAG,KAAKhE,SAAS,IACrC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACO,MAAM,KAAKjE,SAAS,EACxC;MACA,MAAM,IAAI8D,KAAK,CACb,sEACF,CAAC;IACH;IAEA,IACE,IAAI,CAAC1H,MAAM,CAACsH,OAAO,CAACK,MAAM,KAAK/D,SAAS,IACxC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACM,GAAG,KAAKhE,SAAS,IACrC,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACO,MAAM,KAAKjE,SAAS,EACxC;MACA,MAAM,IAAI8D,KAAK,CACb,+EACF,CAAC;IACH;EACF;EAEQ/G,YAAYA,CAAA,EAAY;IAC9B,IAAI,CAAC,IAAI,CAACX,MAAM,CAACsH,OAAO,EAAE;MACxB,OAAO,IAAI;IACb;IAEA,MAAM;MAAEG,KAAK;MAAEE;IAAO,CAAC,GAAG,IAAI,CAACxK,QAAQ,CAACqH,WAAW,CAAC,CAAC;IAErD,IAAI+C,IAAI,GAAG,CAAC;IACZ,IAAIK,GAAG,GAAG,CAAC;IACX,IAAIJ,KAAa,GAAGC,KAAK;IACzB,IAAII,MAAc,GAAGF,MAAM;IAE3B,IAAI,IAAI,CAAC3H,MAAM,CAACsH,OAAO,CAACQ,UAAU,KAAKlE,SAAS,EAAE;MAChD2D,IAAI,IAAI,IAAI,CAACvH,MAAM,CAACsH,OAAO,CAACQ,UAAU;MACtCN,KAAK,IAAI,IAAI,CAACxH,MAAM,CAACsH,OAAO,CAACQ,UAAU;IACzC;IAEA,IAAI,IAAI,CAAC9H,MAAM,CAACsH,OAAO,CAACS,QAAQ,KAAKnE,SAAS,EAAE;MAC9CgE,GAAG,IAAI,IAAI,CAAC5H,MAAM,CAACsH,OAAO,CAACS,QAAQ;MACnCF,MAAM,IAAI,IAAI,CAAC7H,MAAM,CAACsH,OAAO,CAACS,QAAQ;IACxC;IAEA,IAAI,IAAI,CAAC/H,MAAM,CAACsH,OAAO,CAACC,IAAI,KAAK3D,SAAS,EAAE;MAC1C2D,IAAI,GAAG,CAAC,IAAI,CAACvH,MAAM,CAACsH,OAAO,CAACC,IAAI;IAClC;IAEA,IAAI,IAAI,CAACvH,MAAM,CAACsH,OAAO,CAACE,KAAK,KAAK5D,SAAS,EAAE;MAC3C4D,KAAK,GAAGC,KAAK,GAAG,IAAI,CAACzH,MAAM,CAACsH,OAAO,CAACE,KAAK;IAC3C;IAEA,IAAI,IAAI,CAACxH,MAAM,CAACsH,OAAO,CAACM,GAAG,KAAKhE,SAAS,EAAE;MACzCgE,GAAG,GAAG,CAAC,IAAI,CAAC5H,MAAM,CAACsH,OAAO,CAACM,GAAG;IAChC;IAEA,IAAI,IAAI,CAAC5H,MAAM,CAACsH,OAAO,CAACO,MAAM,KAAKjE,SAAS,EAAE;MAC5CiE,MAAM,GAAGF,MAAM,GAAG,IAAI,CAAC3H,MAAM,CAACsH,OAAO,CAACO,MAAM;IAC9C;IAEA,IAAI,IAAI,CAAC7H,MAAM,CAACsH,OAAO,CAACG,KAAK,KAAK7D,SAAS,EAAE;MAC3C,IAAI,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACC,IAAI,KAAK3D,SAAS,EAAE;QAC1C4D,KAAK,GAAGD,IAAI,GAAG,IAAI,CAACvH,MAAM,CAACsH,OAAO,CAACG,KAAK;MAC1C,CAAC,MAAM,IAAI,IAAI,CAACzH,MAAM,CAACsH,OAAO,CAACE,KAAK,KAAK5D,SAAS,EAAE;QAClD2D,IAAI,GAAGC,KAAK,GAAG,IAAI,CAACxH,MAAM,CAACsH,OAAO,CAACG,KAAK;MAC1C;IACF;IAEA,IAAI,IAAI,CAACzH,MAAM,CAACsH,OAAO,CAACK,MAAM,KAAK/D,SAAS,EAAE;MAC5C,IAAI,IAAI,CAAC5D,MAAM,CAACsH,OAAO,CAACM,GAAG,KAAKhE,SAAS,EAAE;QACzCiE,MAAM,GAAGD,GAAG,GAAG,IAAI,CAAC5H,MAAM,CAACsH,OAAO,CAACK,MAAM;MAC3C,CAAC,MAAM,IAAI,IAAI,CAAC3H,MAAM,CAACsH,OAAO,CAACO,MAAM,KAAKjE,SAAS,EAAE;QACnDgE,GAAG,GAAGC,MAAM,GAAG,IAAI,CAAC7H,MAAM,CAACsH,OAAO,CAACK,MAAM;MAC3C;IACF;IAEA,MAAMpD,IAAI,GAAG,IAAI,CAACpH,QAAQ,CAACqH,WAAW,CAAC,CAAC;IAExC,MAAMmC,UAAU,GAAG,IAAI,CAAClH,OAAO,CAACuI,qBAAqB,CAAC,CAAC;IAEvD,IAAI,CAACrB,UAAU,EAAE;MACf,OAAO,KAAK;IACd;IAEA,MAAMsB,OAAe,GAAGtB,UAAU,CAACrB,CAAC,GAAGf,IAAI,CAACiB,KAAK;IACjD,MAAM0C,OAAe,GAAGvB,UAAU,CAAClB,CAAC,GAAGlB,IAAI,CAACmB,KAAK;IAEjD,OACEuC,OAAO,IAAIV,IAAI,IAAIU,OAAO,IAAIT,KAAK,IAAIU,OAAO,IAAIN,GAAG,IAAIM,OAAO,IAAIL,MAAM;EAE9E;EAEOM,gBAAgBA,CAACC,WAAoC,EAAE;IAC5D,OACE,CAACA,WAAW,IACX,CAAC,IAAI,CAACpI,MAAM,CAACoI,WAAW,IAAIA,WAAW,KAAKC,iCAAW,CAACC,IAAK,IAC7D,IAAI,CAACtI,MAAM,CAACoI,WAAW,IAAIA,WAAW,GAAG,IAAI,CAACpI,MAAM,CAACoI,WAAY;EAEtE;EAEUG,WAAWA,CAAA,EAAS,CAAC;EAExBC,SAASA,CAAA,EAAS;IACvBpI,mCAA0B,CAACC,QAAQ,CAAC6G,6BAA6B,CAAC,IAAI,CAAC;IACvE,IAAI,CAAC/J,QAAQ,CAACsL,OAAO,CAAC,IAAI,CAACzI,MAAM,CAAC;EACpC;;EAEA;EACA;EACA;;EAEA,IAAWkE,UAAUA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACwE,WAAW;EACzB;EACA,IAAWxE,UAAUA,CAACrC,KAAa,EAAE;IACnC,IAAI,CAAC6G,WAAW,GAAG7G,KAAK;EAC1B;EAEA,IAAW7B,MAAMA,CAAA,EAAW;IAC1B,OAAO,IAAI,CAACzD,OAAO;EACrB;EAEA,IAAWY,QAAQA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,SAAS;EACvB;EAEA,IAAWqC,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAChD,QAAQ;EACtB;EAEA,IAAWe,KAAKA,CAAA,EAAU;IACxB,OAAO,IAAI,CAACvB,MAAM;EACpB;EACA,IAAcuB,KAAKA,CAACqE,KAAY,EAAE;IAChC,IAAI,CAAC5F,MAAM,GAAG4F,KAAK;EACrB;EAEA,IAAWgB,uBAAuBA,CAAA,EAAG;IACnC,OAAO,IAAI,CAACzG,wBAAwB;EACtC;EACA,IAAcyG,uBAAuBA,CAAChB,KAAK,EAAE;IAC3C,IAAI,CAACzF,wBAAwB,GAAGyF,KAAK;EACvC;EAEA,IAAWrF,OAAOA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACF,QAAQ;EACtB;EACA,IAAcE,OAAOA,CAACqF,KAAK,EAAE;IAC3B,IAAI,CAACvF,QAAQ,GAAGuF,KAAK;EACvB;EAEA,IAAWW,WAAWA,CAAA,EAAgB;IACpC,OAAO,IAAI,CAACzF,YAAY;EAC1B;EACA,IAAcyF,WAAWA,CAACX,KAAkB,EAAE;IAC5C,IAAI,CAAC9E,YAAY,GAAG8E,KAAK;EAC3B;EAEA,IAAWmB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACnG,OAAO;EACrB;EACA,IAAcmG,MAAMA,CAACnB,KAAK,EAAE;IAC1B,IAAI,CAAChF,OAAO,GAAGgF,KAAK;EACtB;EAEA,IAAW8G,QAAQA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC/L,SAAS;EACvB;EACA,IAAc+L,QAAQA,CAAC9G,KAAK,EAAE;IAC5B,IAAI,CAACjF,SAAS,GAAGiF,KAAK;EACxB;EAEA,IAAW+G,eAAeA,CAAA,EAAG;IAC3B,OAAO,IAAI,CAACjM,gBAAgB;EAC9B;EACA,IAAciM,eAAeA,CAAC/G,KAAK,EAAE;IACnC,IAAI,CAAClF,gBAAgB,GAAGkF,KAAK;EAC/B;EAEA,IAAWF,mBAAmBA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAC7E,oBAAoB;EAClC;EACA,IAAc6E,mBAAmBA,CAACE,KAAK,EAAE;IACvC,IAAI,CAAC/E,oBAAoB,GAAG+E,KAAK;EACnC;EAEOgH,oBAAoBA,CAAA,EAAa;IACtC,OAAO,IAAI,CAACpJ,OAAO,CAACqJ,kBAAkB;EACxC;EAEQ5I,UAAUA,CAAA,EAAY;IAC5B,OACE,IAAI,CAAC1C,KAAK,KAAKtB,YAAK,CAACuF,GAAG,IACxB,IAAI,CAACjE,KAAK,KAAKtB,YAAK,CAAC8E,MAAM,IAC3B,IAAI,CAACxD,KAAK,KAAKtB,YAAK,CAACgF,SAAS;EAElC;AACF;AAAC6H,OAAA,CAAAjN,OAAA,GAAAC,cAAA;AAED,SAASwH,oBAAoBA,CAC3ByF,MAG+C,EAC/C1G,KAAqC,EAC/B;EACN,IAAI,CAAC0G,MAAM,EAAE;IACX;EACF;EAEA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;IAChCA,MAAM,CAAC1G,KAAK,CAAC;IACb;EACF;EAEA,IAAI,cAAc,IAAI0G,MAAM,IAAI,OAAOA,MAAM,CAACC,YAAY,KAAK,UAAU,EAAE;IACzE,MAAMlH,OAAO,GAAGiH,MAAM,CAACC,YAAY,CAAC,CAAC;IACrC1F,oBAAoB,CAACxB,OAAO,EAAEO,KAAK,CAAC;IACpC;EACF;EAEA,IAAI,EAAE,cAAc,IAAI0G,MAAM,CAAC,EAAE;IAC/B;EACF;EAEA,MAAM;IAAEE;EAAoC,CAAC,GAAGF,MAAM,CAACG,YAAY;EACnE,IAAI,CAACC,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IAC9B;EACF;EAEA,KAAK,MAAM,CAACI,KAAK,EAAE,CAACpE,GAAG,EAAErD,KAAK,CAAC,CAAC,IAAIqH,UAAU,CAACK,OAAO,CAAC,CAAC,EAAE;IACxD,IAAI,EAAErE,GAAG,IAAI5C,KAAK,CAACqB,WAAW,CAAC,EAAE;MAC/B;IACF;;IAEA;IACA,MAAM6F,WAAW,GAAGlH,KAAK,CAACqB,WAAW,CAACuB,GAAG,CAAC;;IAE1C;IACA,IAAIrD,KAAK,EAAE4H,QAAQ,EAAE;MACnB;MACA;MACA5H,KAAK,CAAC4H,QAAQ,CAACD,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAR,MAAM,CAACG,YAAY,CAACD,UAAU,CAACI,KAAK,CAAC,GAAG,CAACpE,GAAG,EAAEsE,WAAW,CAAC;IAC5D;EACF;EAEA;AACF", "ignoreList": []}