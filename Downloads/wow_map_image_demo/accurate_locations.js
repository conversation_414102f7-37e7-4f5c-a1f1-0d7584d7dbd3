// Accurate WoW Classic Location Data
// Based on known coordinates from addons and community sources
// Coordinates are for 13000x12000 high-resolution map
// Reference: https://www.reddit.com/r/classicwow/comments/bcf5av/

const accurateLocationData = [
  // === MAJOR CITIES ===
  // Alliance Cities
  {
    "name": "Stormwind City",
    "type": "city",
    "faction": "Alliance",
    "coords": [5070, 6480], // ~39, 54 in Elwynn Forest (corrected for 13000x12000)
    "zone": "Elwynn Forest",
    "description": "Capital city of the Alliance",
    "level": "1-60"
  },
  {
    "name": "Ironforge",
    "type": "city",
    "faction": "Alliance",
    "coords": [3380, 6480], // ~26, 54 in Dun Morogh (corrected for 13000x12000)
    "zone": "Dun Morogh",
    "description": "Dwarven capital city",
    "level": "1-60"
  },
  {
    "name": "Darnassus",
    "type": "city",
    "faction": "Alliance",
    "coords": [4680, 3600], // ~36, 30 in Teldrassil (corrected for 13000x12000)
    "zone": "Teldrassil",
    "description": "Night Elf capital city",
    "level": "1-60"
  },

  // Horde Cities
  {
    "name": "Orgrimmar",
    "type": "city",
    "faction": "Horde",
    "coords": [5850, 7560], // ~45, 63 in Durotar (corrected for 13000x12000)
    "zone": "Durotar",
    "description": "Capital city of the Horde",
    "level": "1-60"
  },
  {
    "name": "Thunder Bluff",
    "type": "city",
    "faction": "Horde",
    "coords": [3900, 8640], // ~30, 72 in Mulgore (corrected for 13000x12000)
    "zone": "Mulgore",
    "description": "Tauren capital city",
    "level": "1-60"
  },
  {
    "name": "Undercity",
    "type": "city",
    "faction": "Horde",
    "coords": [8450, 4680], // ~65, 39 in Tirisfal Glades (corrected for 13000x12000)
    "zone": "Tirisfal Glades",
    "description": "Undead capital city",
    "level": "1-60"
  },
  
  // === MAJOR TOWNS ===
  {
    "name": "Goldshire",
    "type": "town",
    "faction": "Alliance",
    "coords": [5460, 6120], // ~42, 51 in Elwynn Forest (corrected for 13000x12000)
    "zone": "Elwynn Forest",
    "description": "Starting town for humans",
    "level": "1-10"
  },
  {
    "name": "Crossroads",
    "type": "town",
    "faction": "Horde",
    "coords": [6760, 9120], // ~52, 76 in The Barrens (corrected for 13000x12000)
    "zone": "The Barrens",
    "description": "Major Horde outpost",
    "level": "10-25"
  },
  {
    "name": "Booty Bay",
    "type": "town",
    "faction": "Neutral",
    "coords": [3640, 11160], // ~28, 93 in Stranglethorn Vale (corrected for 13000x12000)
    "zone": "Stranglethorn Vale",
    "description": "Goblin trading port",
    "level": "30-45"
  },
  {
    "name": "Gadgetzan",
    "type": "town",
    "faction": "Neutral",
    "coords": [6630, 9720], // ~51, 81 in Tanaris (corrected for 13000x12000)
    "zone": "Tanaris",
    "description": "Goblin desert town",
    "level": "40-50"
  },
  {
    "name": "Darkshire",
    "type": "town",
    "faction": "Alliance",
    "coords": [9750, 7020], // ~75, 58 in Duskwood (corrected for 13000x12000)
    "zone": "Duskwood",
    "description": "Spooky Alliance town",
    "level": "20-30"
  },
  {
    "name": "Lakeshire",
    "type": "town",
    "faction": "Alliance",
    "coords": [3640, 3600], // ~28, 30 in Redridge Mountains (corrected for 13000x12000)
    "zone": "Redridge Mountains",
    "description": "Alliance mountain town",
    "level": "15-25"
  },
  
  // === FLIGHT PATHS ===
  {
    "name": "Menethil Harbor",
    "type": "flight_path",
    "faction": "Alliance",
    "coords": [819, 4669], // ~10, 85 in Wetlands
    "zone": "Wetlands",
    "description": "Alliance port and flight path",
    "level": "20-30"
  },
  {
    "name": "Auberdine",
    "type": "flight_path",
    "faction": "Alliance", 
    "coords": [3194, 3440], // ~39, 63 in Darkshore
    "zone": "Darkshore",
    "description": "Night Elf port town",
    "level": "10-20"
  },
  {
    "name": "Ratchet",
    "type": "flight_path",
    "faction": "Neutral",
    "coords": [5248, 3932], // ~64, 72 in The Barrens
    "zone": "The Barrens",
    "description": "Goblin port town",
    "level": "15-25"
  },
  
  // === DUNGEONS ===
  {
    "name": "The Deadmines",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [3440, 1474], // ~42, 27 in Westfall
    "zone": "Westfall",
    "description": "Level 15-25 dungeon",
    "level": "15-25"
  },
  {
    "name": "Wailing Caverns",
    "type": "dungeon", 
    "faction": "Neutral",
    "coords": [3768, 2949], // ~46, 54 in The Barrens
    "zone": "The Barrens",
    "description": "Level 15-25 dungeon",
    "level": "15-25"
  },
  {
    "name": "Shadowfang Keep",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [1802, 1310], // ~22, 24 in Silverpine Forest
    "zone": "Silverpine Forest",
    "description": "Level 20-30 dungeon",
    "level": "20-30"
  },
  {
    "name": "Stockade",
    "type": "dungeon",
    "faction": "Alliance",
    "coords": [3194, 2949], // Inside Stormwind
    "zone": "Stormwind City", 
    "description": "Level 20-30 dungeon",
    "level": "20-30"
  },
  {
    "name": "Razorfen Kraul",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [3440, 4177], // ~42, 76 in The Barrens
    "zone": "The Barrens",
    "description": "Level 25-35 dungeon",
    "level": "25-35"
  },
  {
    "name": "Scarlet Monastery",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [6969, 1474], // ~85, 27 in Tirisfal Glades
    "zone": "Tirisfal Glades",
    "description": "Level 30-45 dungeon complex",
    "level": "30-45"
  },
  {
    "name": "Uldaman",
    "type": "dungeon",
    "faction": "Neutral", 
    "coords": [3440, 1802], // ~42, 33 in Badlands
    "zone": "Badlands",
    "description": "Level 35-45 dungeon",
    "level": "35-45"
  },
  {
    "name": "Zul'Farrak",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [3194, 3932], // ~39, 72 in Tanaris
    "zone": "Tanaris",
    "description": "Level 45-55 dungeon",
    "level": "45-55"
  },
  {
    "name": "Blackrock Depths",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [4177, 2129], // ~51, 39 in Blackrock Mountain
    "zone": "Blackrock Mountain",
    "description": "Level 50-60 dungeon",
    "level": "50-60"
  },
  {
    "name": "Blackrock Spire",
    "type": "dungeon",
    "faction": "Neutral",
    "coords": [4177, 2129], // ~51, 39 in Blackrock Mountain
    "zone": "Blackrock Mountain", 
    "description": "Level 55-60 dungeon",
    "level": "55-60"
  },
  
  // === RAIDS ===
  {
    "name": "Molten Core",
    "type": "raid",
    "faction": "Neutral",
    "coords": [4177, 2129], // Inside Blackrock Depths
    "zone": "Blackrock Mountain",
    "description": "40-man raid",
    "level": "60"
  },
  {
    "name": "Onyxia's Lair",
    "type": "raid",
    "faction": "Neutral",
    "coords": [4423, 5248], // ~54, 96 in Dustwallow Marsh
    "zone": "Dustwallow Marsh",
    "description": "40-man raid",
    "level": "60"
  },
  {
    "name": "Blackwing Lair",
    "type": "raid",
    "faction": "Neutral",
    "coords": [4177, 2129], // Top of Blackrock Spire
    "zone": "Blackrock Mountain",
    "description": "40-man raid",
    "level": "60"
  }
];

// Coordinate conversion utility for 13000x12000 map
function convertWowCoordsToMap(wowX, wowY, mapWidth = 13000, mapHeight = 12000) {
  // Convert WoW percentage coordinates (0-100) to map pixel coordinates
  return [
    Math.round((wowX / 100) * mapWidth),
    Math.round((wowY / 100) * mapHeight)
  ];
}

// Helper function to validate coordinates are within map bounds
function validateCoordinates(coords, mapWidth = 13000, mapHeight = 12000) {
  const [x, y] = coords;
  return x >= 0 && x <= mapWidth && y >= 0 && y <= mapHeight;
}

// Export for React Native
export {
  accurateLocationData,
  convertWowCoordsToMap,
  validateCoordinates
};

// Export for use in main map (web compatibility)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    accurateLocationData,
    convertWowCoordsToMap,
    validateCoordinates
  };
}

if (typeof window !== 'undefined') {
  window.accurateLocationData = accurateLocationData;
  window.convertWowCoordsToMap = convertWowCoordsToMap;
  window.validateCoordinates = validateCoordinates;
  console.log('Loaded', accurateLocationData.length, 'accurate WoW Classic locations for 13000x12000 map');
}
