# WoW Classic Interactive Map - React Native App

An interactive mobile application for exploring the World of Warcraft Classic map with location markers, multi-language support, and coordinate tools.

## Features

🗺️ **Interactive Map**
- High-resolution 13000x12000 WoW Classic terrain map
- Zoom, pan, and explore the entire world
- Tap coordinates to get exact map positions

📍 **Location Markers**
- Cities, towns, dungeons, raids, and flight paths
- Color-coded markers by location type
- Detailed information for each location

🌍 **Multi-Language Support**
- English, Chinese (Simplified), Spanish
- Translated location names and descriptions
- Persistent language preferences

🔍 **Search & Filter**
- Search locations by name, zone, or description
- Filter by location type (cities, dungeons, etc.)
- Real-time filtering and search results

📱 **Mobile Optimized**
- Native iOS and Android support
- Gesture-based navigation
- Responsive design for all screen sizes

## Screenshots

[Add screenshots here when available]

## Installation

### Prerequisites

- Node.js (>= 16)
- React Native CLI
- Xcode (for iOS development)
- Android Studio (for Android development)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd wow-classic-map-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **iOS Setup**
   ```bash
   cd ios && pod install && cd ..
   ```

4. **Run the app**
   
   For iOS:
   ```bash
   npm run ios
   ```
   
   For Android:
   ```bash
   npm run android
   ```

## Development

### Project Structure

```
src/
├── components/          # React Native components
│   ├── WowMapViewer.js     # Main map component
│   ├── LanguageSelector.js # Language selection
│   ├── LocationSearch.js   # Search functionality
│   ├── FilterControls.js   # Location type filters
│   └── LocationDetails.js  # Location detail modal
├── data/
│   └── locations.js     # Location data and utilities
├── utils/
│   └── translations.js  # Multi-language support
└── assets/
    └── wowmap.png      # High-resolution map image
```

### Key Components

#### WowMapViewer
- Main interactive map component
- Handles zoom, pan, and marker rendering
- Coordinate click detection

#### LocationDetails
- Modal component for location information
- Displays translated names and descriptions
- Coordinate copying functionality

#### LanguageSelector
- Dropdown for language selection
- Persistent language preferences
- Real-time UI updates

### Adding New Locations

1. **Add location data** in `src/data/locations.js`:
   ```javascript
   {
     name: "Location Name",
     type: "city|town|dungeon|raid|flight_path|landmark",
     faction: "Alliance|Horde|Neutral",
     coords: [x, y], // Map coordinates for 13000x12000 map
     zone: "Zone Name",
     description: "Location description",
     level: "Level range"
   }
   ```

2. **Add translations** in `src/utils/translations.js`:
   ```javascript
   locations: {
     "Location Name": {
       name: "Translated Name",
       description: "Translated description"
     }
   }
   ```

### Coordinate System

The app uses a 13000x12000 pixel coordinate system based on the high-resolution WoW Classic terrain map.

- **Map Dimensions**: 13000 x 12000 pixels
- **Coordinate Origin**: Top-left corner (0, 0)
- **Conversion**: WoW percentage coordinates can be converted using the `convertWowCoordsToMap` utility

### Map Image

The app requires the high-resolution WoW Classic terrain map:
- **File**: `assets/wowmap.png`
- **Dimensions**: 13000 x 12000 pixels
- **Source**: [Reddit Community Creation](https://www.reddit.com/r/classicwow/comments/bcf5av/)
- **Download**: [Archive.org](https://archive.org/details/wow_classic_high_resolution_world_terrain_map_azeroth)

## Building for Production

### Android

1. **Generate signed APK**:
   ```bash
   cd android
   ./gradlew assembleRelease
   ```

2. **Generate AAB for Play Store**:
   ```bash
   ./gradlew bundleRelease
   ```

### iOS

1. **Archive for App Store**:
   ```bash
   cd ios
   xcodebuild -workspace WowClassicMap.xcworkspace -scheme WowClassicMap -configuration Release -destination generic/platform=iOS -archivePath WowClassicMap.xcarchive archive
   ```

## Dependencies

### Core
- React Native 0.72.6
- React 18.2.0

### Map & Gestures
- react-native-image-pan-zoom
- react-native-gesture-handler
- react-native-reanimated

### UI Components
- react-native-picker-select
- react-native-vector-icons
- react-native-svg

### Storage
- @react-native-async-storage/async-storage

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Credits

- **Map Image**: Community-created high-resolution WoW Classic terrain map
- **Location Data**: Based on WoW Classic addon databases and community sources
- **Translations**: Community contributions

## Support

For issues and feature requests, please use the GitHub issue tracker.

## Roadmap

- [ ] Offline map support
- [ ] Custom marker creation
- [ ] Route planning between locations
- [ ] Integration with WoW Classic APIs
- [ ] Additional language support
- [ ] Dark/light theme toggle
- [ ] Location sharing functionality
