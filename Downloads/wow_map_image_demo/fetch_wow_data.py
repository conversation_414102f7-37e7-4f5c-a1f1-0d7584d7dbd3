#!/usr/bin/env python3
"""
WoW Classic Data Fetcher
Fetches location data from Wowhead and ClassicDB
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import csv

class WowDataFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # WoW Classic zone IDs
        self.zones = {
            12: "Elwynn Forest",
            1: "Dun Morogh", 
            14: "Durotar",
            17: "The Barrens",
            215: "Mulgore",
            85: "Tirisfal Glades",
            141: "Teldrassil",
            40: "Westfall",
            44: "Redridge Mountains",
            10: "Duskwood",
            39: "Stranglethorn Vale",
            440: "Tanaris",
            # Add more zones as needed
        }
    
    def fetch_wowhead_zone(self, zone_id, zone_name):
        """Fetch data from a specific Wowhead zone page"""
        url = f"https://classic.wowhead.com/zone={zone_id}"
        
        try:
            print(f"Fetching {zone_name} (ID: {zone_id})...")
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            locations = []
            
            # Find all location data in the page
            # Look for coordinate patterns in various elements
            coord_pattern = r'(\d+\.?\d*),\s*(\d+\.?\d*)'
            
            # Extract from listview rows (NPCs, objects, etc.)
            listview_rows = soup.find_all('tr', class_='listview-row')
            for row in listview_rows:
                name_elem = row.find('a', class_='q')
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    
                    # Look for coordinates in the row
                    row_text = row.get_text()
                    coord_match = re.search(coord_pattern, row_text)
                    
                    if coord_match:
                        x, y = float(coord_match.group(1)), float(coord_match.group(2))
                        
                        # Determine type based on row attributes or content
                        location_type = 'unknown'
                        if 'npc' in row.get('data-type', ''):
                            location_type = 'npc'
                        elif 'object' in row.get('data-type', ''):
                            location_type = 'object'
                        
                        locations.append({
                            'name': name,
                            'type': location_type,
                            'coords': [x, y],
                            'zone': zone_name,
                            'source': 'wowhead'
                        })
            
            return {
                'zone_id': zone_id,
                'zone_name': zone_name,
                'locations': locations,
                'url': url
            }
            
        except Exception as e:
            print(f"Error fetching {zone_name}: {e}")
            return None
    
    def fetch_classicdb_data(self):
        """Fetch data from ClassicDB"""
        base_url = "https://classicdb.ch"
        
        try:
            # This would need to be customized based on ClassicDB's actual structure
            print("Fetching from ClassicDB...")
            response = self.session.get(f"{base_url}/zones", timeout=10)
            response.raise_for_status()
            
            # Parse ClassicDB data (structure may vary)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract zone and location data
            # This is a placeholder - actual implementation depends on site structure
            
            return {"message": "ClassicDB fetching needs site-specific implementation"}
            
        except Exception as e:
            print(f"Error fetching from ClassicDB: {e}")
            return None
    
    def convert_coordinates(self, wow_x, wow_y, map_width=8192, map_height=6144):
        """Convert WoW percentage coordinates to map pixel coordinates"""
        pixel_x = int((wow_x / 100) * map_width)
        pixel_y = int((wow_y / 100) * map_height)
        return [pixel_x, pixel_y]
    
    def fetch_all_zones(self):
        """Fetch data from all zones"""
        all_data = {}
        
        for zone_id, zone_name in self.zones.items():
            zone_data = self.fetch_wowhead_zone(zone_id, zone_name)
            if zone_data:
                all_data[zone_id] = zone_data
            
            # Be respectful - wait between requests
            time.sleep(2)
        
        return all_data
    
    def save_to_json(self, data, filename='wow_classic_data.json'):
        """Save data to JSON file"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Data saved to {filename}")
    
    def save_to_csv(self, data, filename='wow_classic_locations.csv'):
        """Save location data to CSV file"""
        locations = []
        
        for zone_data in data.values():
            if 'locations' in zone_data:
                locations.extend(zone_data['locations'])
        
        if locations:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=['name', 'type', 'coords', 'zone', 'source'])
                writer.writeheader()
                writer.writerows(locations)
            print(f"CSV data saved to {filename}")
    
    def create_js_data(self, data, filename='wow_locations.js'):
        """Create JavaScript file with location data"""
        locations = []
        
        for zone_data in data.values():
            if 'locations' in zone_data:
                for loc in zone_data['locations']:
                    # Convert coordinates for map usage
                    converted_coords = self.convert_coordinates(loc['coords'][0], loc['coords'][1])
                    
                    locations.append({
                        'name': loc['name'],
                        'type': loc['type'],
                        'coords': converted_coords,
                        'zone': loc['zone'],
                        'faction': 'Neutral',  # Default, would need more logic to determine
                        'description': f"{loc['type'].title()} in {loc['zone']}"
                    })
        
        js_content = f"""// Auto-generated WoW Classic location data
// Generated from Wowhead and other sources

const fetchedLocationData = {json.dumps(locations, indent=2)};

// Export for use in main map
if (typeof module !== 'undefined' && module.exports) {{
  module.exports = {{ fetchedLocationData }};
}}

if (typeof window !== 'undefined') {{
  window.fetchedLocationData = fetchedLocationData;
  console.log('Loaded', fetchedLocationData.length, 'locations from fetched data');
}}
"""
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(js_content)
        print(f"JavaScript data saved to {filename}")

def main():
    """Main function to run the data fetcher"""
    fetcher = WowDataFetcher()
    
    print("Starting WoW Classic data fetch...")
    print("This may take several minutes to be respectful to servers...")
    
    # Fetch all zone data
    all_data = fetcher.fetch_all_zones()
    
    # Save in multiple formats
    fetcher.save_to_json(all_data)
    fetcher.save_to_csv(all_data)
    fetcher.create_js_data(all_data)
    
    print(f"Fetching complete! Found data for {len(all_data)} zones.")
    
    # Print summary
    total_locations = sum(len(zone.get('locations', [])) for zone in all_data.values())
    print(f"Total locations found: {total_locations}")

if __name__ == "__main__":
    main()
