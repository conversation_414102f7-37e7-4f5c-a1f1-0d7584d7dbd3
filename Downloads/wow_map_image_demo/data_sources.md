# WoW Classic Location Data Sources

## 1. Wowhead Database
- **URL**: https://classic.wowhead.com/zones
- **Method**: Use browser dev tools to extract coordinates
- **Data Available**: NPCs, quests, items, flight paths

## 2. WoW Classic Addons
### Atlas (Dungeon Maps)
- **GitHub**: https://github.com/Resike/Atlas
- **Data**: Dungeon and raid coordinates

### Questie (Quest Database)
- **GitHub**: https://github.com/AeroScripts/QuestieDev
- **Data**: Quest locations, NPCs, objectives

### HandyNotes
- **CurseForge**: Various HandyNotes plugins
- **Data**: Treasure, rare spawns, flight paths

## 3. Community Databases
### WoW Classic DB
- **URL**: https://classicdb.ch/
- **API**: Limited but has coordinate data

### Vanilla WoW Database
- **URL**: http://vanillawowdb.com/
- **Data**: Items, NPCs, quests with coordinates

## 4. Manual Data Collection Methods

### Browser Console <PERSON>t for Wowhead
```javascript
// Run this on Wowhead zone pages to extract coordinates
var locations = [];
document.querySelectorAll('.listview-row').forEach(function(row) {
  var name = row.querySelector('.q')?.textContent;
  var coords = row.querySelector('.coordinates')?.textContent;
  if (name && coords) {
    locations.push({name: name, coords: coords});
  }
});
console.log(JSON.stringify(locations, null, 2));
```

### Python Script for Data Scraping
```python
import requests
from bs4 import BeautifulSoup
import json

def scrape_wowhead_zone(zone_id):
    url = f"https://classic.wowhead.com/zone={zone_id}"
    # Add scraping logic here
    pass
```

## 5. Pre-made Datasets
### Flight Paths
- All major flight path coordinates
- Connect cities and towns

### Dungeon Entrances
- All Classic dungeon entrance coordinates
- Includes level ranges

### Major Cities
- Capital cities for both factions
- Important neutral towns

## 6. API Sources
### Battle.net API (Limited for Classic)
- **URL**: https://develop.battle.net/
- **Note**: Mainly for retail WoW

### Community APIs
- Various fan-made APIs with Classic data
- Check GitHub for "wow classic api"

## 7. Game Files
### WoW Client Data
- Extract from game installation
- DBC files contain coordinate data
- Requires specialized tools

## Usage Tips
1. Start with manual curated data (like in your current file)
2. Use Wowhead for specific locations
3. Scrape data responsibly (respect rate limits)
4. Combine multiple sources for completeness
5. Validate coordinates on your map
