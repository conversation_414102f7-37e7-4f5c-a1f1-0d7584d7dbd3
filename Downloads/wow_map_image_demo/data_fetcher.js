// WoW Classic Data Fetcher for Wowhead and ClassicDB
// This script helps extract location data from various sources

// Wowhead zone IDs for WoW Classic
const wowheadZones = {
  // Eastern Kingdoms
  1: "Dun Morogh",
  12: "Elwynn Forest", 
  38: "Loch Modan",
  40: "Westfall",
  41: "Deadwind Pass",
  44: "Redridge Mountains",
  47: "The Hinterlands",
  51: "Searing Gorge",
  46: "Burning Steppes",
  36: "Alterac Mountains",
  45: "Arathi Highlands",
  3: "Badlands",
  4: "Blasted Lands",
  85: "Tirisfal Glades",
  130: "Silverpine Forest",
  267: "Hillsbrad Foothills",
  28: "Western Plaguelands",
  139: "Eastern Plaguelands",
  11: "Wetlands",
  39: "Stranglethorn Vale",
  8: "Swamp of Sorrows",
  10: "Duskwood",
  
  // Kalimdor
  141: "Teldrassil",
  148: "Darkshore",
  331: "Ashenvale",
  400: "Thousand Needles",
  405: "Desolace",
  406: "Stonetalon Mountains",
  440: "Tanaris",
  490: "Un'Goro Crater",
  493: "Moonglade",
  618: "Winterspring",
  1377: "Silithus",
  16: "<PERSON><PERSON><PERSON><PERSON>",
  215: "Mulgore",
  17: "The Barrens",
  14: "Durotar",
  15: "Dustwallow Marsh",
  357: "Feralas"
};

// Browser script to run on Wowhead zone pages
const wowheadExtractor = `
// Run this script in browser console on Wowhead zone pages
function extractWowheadData(zoneId) {
  const data = {
    zone: document.querySelector('h1.heading-size-1')?.textContent || 'Unknown Zone',
    locations: []
  };
  
  // Extract NPCs with coordinates
  const npcRows = document.querySelectorAll('.listview-row[data-type="npc"]');
  npcRows.forEach(row => {
    const name = row.querySelector('.q')?.textContent;
    const coords = row.querySelector('.coordinates')?.textContent;
    const level = row.querySelector('.level')?.textContent;
    
    if (name && coords) {
      const coordMatch = coords.match(/(\\d+\\.?\\d*),\\s*(\\d+\\.?\\d*)/);
      if (coordMatch) {
        data.locations.push({
          name: name.trim(),
          type: 'npc',
          coords: [parseFloat(coordMatch[1]), parseFloat(coordMatch[2])],
          level: level?.trim(),
          zone: data.zone
        });
      }
    }
  });
  
  // Extract objects (chests, herbs, etc.)
  const objectRows = document.querySelectorAll('.listview-row[data-type="object"]');
  objectRows.forEach(row => {
    const name = row.querySelector('.q')?.textContent;
    const coords = row.querySelector('.coordinates')?.textContent;
    
    if (name && coords) {
      const coordMatch = coords.match(/(\\d+\\.?\\d*),\\s*(\\d+\\.?\\d*)/);
      if (coordMatch) {
        data.locations.push({
          name: name.trim(),
          type: 'object',
          coords: [parseFloat(coordMatch[1]), parseFloat(coordMatch[2])],
          zone: data.zone
        });
      }
    }
  });
  
  return data;
}

// Usage: extractWowheadData(12) for Elwynn Forest
console.log('Wowhead extractor loaded. Use: extractWowheadData(zoneId)');
`;

// Node.js script for automated fetching (requires puppeteer)
const automatedFetcher = `
const puppeteer = require('puppeteer');
const fs = require('fs');

async function fetchAllWowheadData() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  const allData = {};
  
  for (const [zoneId, zoneName] of Object.entries(wowheadZones)) {
    try {
      console.log(\`Fetching data for \${zoneName} (ID: \${zoneId})...\`);
      
      await page.goto(\`https://classic.wowhead.com/zone=\${zoneId}\`, {
        waitUntil: 'networkidle2'
      });
      
      // Wait for content to load
      await page.waitForSelector('.listview-row', { timeout: 10000 });
      
      // Extract data
      const zoneData = await page.evaluate(() => {
        // Paste the wowheadExtractor function here
        return extractWowheadData();
      });
      
      allData[zoneId] = zoneData;
      
      // Be respectful - wait between requests
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(\`Error fetching \${zoneName}:\`, error);
    }
  }
  
  // Save to file
  fs.writeFileSync('wow_classic_locations.json', JSON.stringify(allData, null, 2));
  console.log('Data saved to wow_classic_locations.json');
  
  await browser.close();
}

// Run the fetcher
fetchAllWowheadData();
`;

// Manual data collection instructions
const manualInstructions = `
MANUAL DATA COLLECTION GUIDE:

1. WOWHEAD METHOD:
   - Go to https://classic.wowhead.com/zones
   - Click on each zone
   - Open browser console (F12)
   - Paste the wowheadExtractor script
   - Run: extractWowheadData()
   - Copy the JSON output

2. CLASSICDB METHOD:
   - Go to https://classicdb.ch/
   - Navigate to zones section
   - Use similar extraction techniques

3. QUESTIE ADDON DATA:
   - Download Questie addon
   - Extract coordinate data from addon files
   - Location: WoW/Interface/AddOns/Questie/Database/

4. ATLAS ADDON DATA:
   - Download Atlas addon
   - Extract dungeon/raid coordinates
   - Location: WoW/Interface/AddOns/Atlas/

COORDINATE CONVERSION:
- Wowhead uses percentage coordinates (0-100)
- Your map uses pixel coordinates
- Conversion formula: pixelX = (wowheadX / 100) * mapWidth
`;

// Export functions
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    wowheadZones,
    wowheadExtractor,
    automatedFetcher,
    manualInstructions
  };
}

// Browser usage
if (typeof window !== 'undefined') {
  window.wowheadZones = wowheadZones;
  window.wowheadExtractor = wowheadExtractor;
  
  // Helper function to convert Wowhead coordinates to map coordinates
  window.convertCoordinates = function(wowheadX, wowheadY, mapWidth = 8192, mapHeight = 6144) {
    return [
      Math.round((wowheadX / 100) * mapWidth),
      Math.round((wowheadY / 100) * mapHeight)
    ];
  };
  
  console.log('WoW Classic data fetcher loaded!');
  console.log('Available zones:', Object.keys(wowheadZones).length);
}
