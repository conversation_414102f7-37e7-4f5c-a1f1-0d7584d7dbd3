/**
 * WoW Classic Location Data for React Native
 * Accurate coordinates for 13000x12000 high-resolution map
 */

export const accurateLocationData = [
  // === MAJOR CITIES ===
  // Alliance Cities
  {
    name: "Stormwind City",
    type: "city",
    faction: "Alliance",
    coords: [5070, 6480], // ~39, 54 in Elwynn Forest (corrected for 13000x12000)
    zone: "Elwynn Forest",
    description: "Capital city of the Alliance",
    level: "1-60"
  },
  {
    name: "Ironforge",
    type: "city",
    faction: "Alliance", 
    coords: [3380, 6480], // ~26, 54 in Dun Morogh (corrected for 13000x12000)
    zone: "Dun Morogh",
    description: "Dwarven capital city",
    level: "1-60"
  },
  {
    name: "Darnassus",
    type: "city",
    faction: "Alliance",
    coords: [4680, 3600], // ~36, 30 in Teldrassil (corrected for 13000x12000)
    zone: "Teldrassil", 
    description: "Night Elf capital city",
    level: "1-60"
  },
  
  // Horde Cities
  {
    name: "Orgrimmar", 
    type: "city",
    faction: "Horde",
    coords: [5850, 7560], // ~45, 63 in Durotar (corrected for 13000x12000)
    zone: "Durotar",
    description: "Capital city of the Horde",
    level: "1-60"
  },
  {
    name: "Thunder Bluff",
    type: "city",
    faction: "Horde",
    coords: [3900, 8640], // ~30, 72 in Mulgore (corrected for 13000x12000)
    zone: "Mulgore",
    description: "Tauren capital city", 
    level: "1-60"
  },
  {
    name: "Undercity",
    type: "city",
    faction: "Horde",
    coords: [8450, 4680], // ~65, 39 in Tirisfal Glades (corrected for 13000x12000)
    zone: "Tirisfal Glades",
    description: "Undead capital city",
    level: "1-60"
  },
  
  // === MAJOR TOWNS ===
  {
    name: "Goldshire",
    type: "town",
    faction: "Alliance",
    coords: [5460, 6120], // ~42, 51 in Elwynn Forest (corrected for 13000x12000)
    zone: "Elwynn Forest",
    description: "Starting town for humans",
    level: "1-10"
  },
  {
    name: "Crossroads",
    type: "town", 
    faction: "Horde",
    coords: [6760, 9120], // ~52, 76 in The Barrens (corrected for 13000x12000)
    zone: "The Barrens",
    description: "Major Horde outpost",
    level: "10-25"
  },
  {
    name: "Booty Bay",
    type: "town",
    faction: "Neutral",
    coords: [3640, 11160], // ~28, 93 in Stranglethorn Vale (corrected for 13000x12000)
    zone: "Stranglethorn Vale",
    description: "Goblin trading port",
    level: "30-45"
  },
  {
    name: "Gadgetzan",
    type: "town",
    faction: "Neutral", 
    coords: [6630, 9720], // ~51, 81 in Tanaris (corrected for 13000x12000)
    zone: "Tanaris",
    description: "Goblin desert town",
    level: "40-50"
  },
  
  // === DUNGEONS ===
  {
    name: "The Deadmines",
    type: "dungeon",
    faction: "Neutral",
    coords: [5460, 3240], // ~42, 27 in Westfall (corrected for 13000x12000)
    zone: "Westfall",
    description: "Level 15-25 dungeon",
    level: "15-25"
  },
  {
    name: "Wailing Caverns",
    type: "dungeon", 
    faction: "Neutral",
    coords: [5980, 6480], // ~46, 54 in The Barrens (corrected for 13000x12000)
    zone: "The Barrens",
    description: "Level 15-25 dungeon",
    level: "15-25"
  },
  {
    name: "Shadowfang Keep",
    type: "dungeon",
    faction: "Neutral",
    coords: [2860, 2880], // ~22, 24 in Silverpine Forest (corrected for 13000x12000)
    zone: "Silverpine Forest",
    description: "Level 20-30 dungeon",
    level: "20-30"
  },
  {
    name: "Blackrock Depths",
    type: "dungeon",
    faction: "Neutral",
    coords: [6630, 4680], // ~51, 39 in Blackrock Mountain (corrected for 13000x12000)
    zone: "Blackrock Mountain",
    description: "Level 50-60 dungeon",
    level: "50-60"
  },
  
  // === RAIDS ===
  {
    name: "Molten Core",
    type: "raid",
    faction: "Neutral",
    coords: [6630, 4680], // Inside Blackrock Depths (corrected for 13000x12000)
    zone: "Blackrock Mountain",
    description: "40-man raid",
    level: "60"
  },
  {
    name: "Onyxia's Lair",
    type: "raid",
    faction: "Neutral",
    coords: [7020, 11520], // ~54, 96 in Dustwallow Marsh (corrected for 13000x12000)
    zone: "Dustwallow Marsh",
    description: "40-man raid",
    level: "60"
  },
  
  // === FLIGHT PATHS ===
  {
    name: "Menethil Harbor",
    type: "flight_path",
    faction: "Alliance",
    coords: [1300, 10200], // ~10, 85 in Wetlands (corrected for 13000x12000)
    zone: "Wetlands",
    description: "Alliance port and flight path",
    level: "20-30"
  },
  {
    name: "Auberdine",
    type: "flight_path",
    faction: "Alliance", 
    coords: [5070, 7560], // ~39, 63 in Darkshore (corrected for 13000x12000)
    zone: "Darkshore",
    description: "Night Elf port town",
    level: "10-20"
  },
  {
    name: "Ratchet",
    type: "flight_path",
    faction: "Neutral",
    coords: [8320, 8640], // ~64, 72 in The Barrens (corrected for 13000x12000)
    zone: "The Barrens",
    description: "Goblin port town",
    level: "15-25"
  },
];

// Coordinate conversion utility for 13000x12000 map
export const convertWowCoordsToMap = (wowX, wowY, mapWidth = 13000, mapHeight = 12000) => {
  // Convert WoW percentage coordinates (0-100) to map pixel coordinates
  return [
    Math.round((wowX / 100) * mapWidth),
    Math.round((wowY / 100) * mapHeight)
  ];
};

// Helper function to validate coordinates are within map bounds
export const validateCoordinates = (coords, mapWidth = 13000, mapHeight = 12000) => {
  const [x, y] = coords;
  return x >= 0 && x <= mapWidth && y >= 0 && y <= mapHeight;
};

// Get location by name
export const getLocationByName = (name) => {
  return accurateLocationData.find(location => 
    location.name.toLowerCase() === name.toLowerCase()
  );
};

// Get locations by type
export const getLocationsByType = (type) => {
  return accurateLocationData.filter(location => location.type === type);
};

// Get locations by faction
export const getLocationsByFaction = (faction) => {
  return accurateLocationData.filter(location => 
    location.faction.toLowerCase() === faction.toLowerCase()
  );
};

// Get locations by zone
export const getLocationsByZone = (zone) => {
  return accurateLocationData.filter(location => 
    location.zone.toLowerCase() === zone.toLowerCase()
  );
};
