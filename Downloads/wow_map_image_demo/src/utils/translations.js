/**
 * Multi-language translation system for WoW Classic Map React Native App
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export const translations = {
  // English (default)
  en: {
    title: "WoW Classic Map",
    mapTitle: "World of Warcraft Classic Map",
    search: "Search locations...",
    
    // Location types
    city: "City",
    town: "Town", 
    dungeon: "Dungeon",
    raid: "Raid",
    flight_path: "Flight Path",
    landmark: "Landmark",
    
    // Factions
    alliance: "Alliance",
    horde: "Horde", 
    neutral: "Neutral",
    
    // UI Labels
    type: "Type",
    zone: "Zone",
    faction: "Faction",
    description: "Description",
    level: "Level",
    coordinates: "Coordinates",
    filters: "Filters",
    
    // Location names and descriptions
    locations: {
      "Stormwind City": {
        name: "Stormwind City",
        description: "Capital city of the Alliance"
      },
      "Ironforge": {
        name: "Ironforge", 
        description: "Dwarven capital city"
      },
      "Darnassus": {
        name: "Darnassus",
        description: "Night Elf capital city"
      },
      "Orgrimmar": {
        name: "Orgrimmar",
        description: "Capital city of the Horde"
      },
      "Thunder Bluff": {
        name: "Thunder Bluff",
        description: "Tauren capital city"
      },
      "Undercity": {
        name: "Undercity",
        description: "Undead capital city"
      },
      "Booty Bay": {
        name: "Booty Bay",
        description: "Goblin trading port"
      },
      "Gadgetzan": {
        name: "Gadgetzan", 
        description: "Goblin desert town"
      },
      "The Deadmines": {
        name: "The Deadmines",
        description: "Level 15-25 dungeon"
      },
      "Molten Core": {
        name: "Molten Core",
        description: "40-man raid"
      }
    }
  },
  
  // Chinese Simplified
  zh: {
    title: "魔兽世界经典版地图",
    mapTitle: "魔兽世界经典版地图",
    search: "搜索位置...",
    
    // Location types
    city: "城市",
    town: "城镇",
    dungeon: "副本", 
    raid: "团队副本",
    flight_path: "飞行点",
    landmark: "地标",
    
    // Factions
    alliance: "联盟",
    horde: "部落",
    neutral: "中立",
    
    // UI Labels
    type: "类型",
    zone: "区域", 
    faction: "阵营",
    description: "描述",
    level: "等级",
    coordinates: "坐标",
    filters: "筛选",
    
    // Location names and descriptions
    locations: {
      "Stormwind City": {
        name: "暴风城",
        description: "联盟的首都"
      },
      "Ironforge": {
        name: "铁炉堡",
        description: "矮人的首都"
      },
      "Darnassus": {
        name: "达纳苏斯", 
        description: "暗夜精灵的首都"
      },
      "Orgrimmar": {
        name: "奥格瑞玛",
        description: "部落的首都"
      },
      "Thunder Bluff": {
        name: "雷霆崖",
        description: "牛头人的首都"
      },
      "Undercity": {
        name: "幽暗城",
        description: "亡灵的首都"
      },
      "Booty Bay": {
        name: "藏宝海湾",
        description: "地精贸易港口"
      },
      "Gadgetzan": {
        name: "加基森",
        description: "地精沙漠城镇"
      },
      "The Deadmines": {
        name: "死亡矿井",
        description: "15-25级副本"
      },
      "Molten Core": {
        name: "熔火之心", 
        description: "40人团队副本"
      }
    }
  },
  
  // Spanish
  es: {
    title: "Mapa de WoW Classic",
    mapTitle: "Mapa de World of Warcraft Classic",
    search: "Buscar ubicaciones...",
    
    // Location types
    city: "Ciudad",
    town: "Pueblo",
    dungeon: "Mazmorra",
    raid: "Banda",
    flight_path: "Punto de Vuelo",
    landmark: "Punto de Referencia",
    
    // Factions
    alliance: "Alianza",
    horde: "Horda",
    neutral: "Neutral",
    
    // UI Labels
    type: "Tipo",
    zone: "Zona",
    faction: "Facción", 
    description: "Descripción",
    level: "Nivel",
    coordinates: "Coordenadas",
    filters: "Filtros",
    
    // Location names and descriptions
    locations: {
      "Stormwind City": {
        name: "Ciudad de Ventormenta",
        description: "Ciudad capital de la Alianza"
      },
      "Ironforge": {
        name: "Forjaz",
        description: "Ciudad capital de los enanos"
      },
      "Darnassus": {
        name: "Darnassus",
        description: "Ciudad capital de los elfos de la noche"
      },
      "Orgrimmar": {
        name: "Orgrimmar", 
        description: "Ciudad capital de la Horda"
      },
      "Thunder Bluff": {
        name: "Cima del Trueno",
        description: "Ciudad capital de los tauren"
      },
      "Undercity": {
        name: "Entrañas",
        description: "Ciudad capital de los no-muertos"
      },
      "Booty Bay": {
        name: "Bahía del Botín",
        description: "Puerto comercial goblin"
      },
      "Gadgetzan": {
        name: "Gadgetzan",
        description: "Ciudad goblin del desierto"
      },
      "The Deadmines": {
        name: "Las Minas de la Muerte",
        description: "Mazmorra de nivel 15-25"
      },
      "Molten Core": {
        name: "Núcleo de Magma",
        description: "Banda de 40 jugadores"
      }
    }
  }
};

// Translation utility functions
export const getCurrentLanguage = async () => {
  try {
    const language = await AsyncStorage.getItem('mapLanguage');
    return language || 'en';
  } catch (error) {
    console.error('Error getting language:', error);
    return 'en';
  }
};

export const setLanguage = async (lang) => {
  try {
    await AsyncStorage.setItem('mapLanguage', lang);
  } catch (error) {
    console.error('Error setting language:', error);
  }
};

export const translate = (key, subkey = null, language = 'en') => {
  const langData = translations[language] || translations.en;
  
  if (subkey) {
    return langData[key] && langData[key][subkey] ? langData[key][subkey] : key;
  }
  
  return langData[key] || key;
};

export const translateLocation = (locationName, property = 'name', language = 'en') => {
  const langData = translations[language] || translations.en;
  
  if (langData.locations && langData.locations[locationName]) {
    return langData.locations[locationName][property] || locationName;
  }
  
  return locationName;
};
