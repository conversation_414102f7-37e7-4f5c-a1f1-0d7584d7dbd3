/**
 * LocationSearch Component
 * Search input for filtering locations
 */

import React from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Text,
} from 'react-native';

const LocationSearch = ({
  searchQuery,
  onSearchChange,
  placeholder = 'Search locations...',
}) => {
  const clearSearch = () => {
    onSearchChange('');
  };

  return (
    <View style={styles.container}>
      <TextInput
        style={styles.searchInput}
        value={searchQuery}
        onChangeText={onSearchChange}
        placeholder={placeholder}
        placeholderTextColor="#999999"
        autoCapitalize="none"
        autoCorrect={false}
      />
      
      {searchQuery.length > 0 && (
        <TouchableOpacity style={styles.clearButton} onPress={clearSearch}>
          <Text style={styles.clearButtonText}>✕</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 8,
    position: 'relative',
  },
  searchInput: {
    flex: 1,
    height: 40,
    backgroundColor: '#404040',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingRight: 40,
    color: '#ffffff',
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#666666',
  },
  clearButton: {
    position: 'absolute',
    right: 12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#666666',
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default LocationSearch;
