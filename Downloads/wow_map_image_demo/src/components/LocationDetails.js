/**
 * LocationDetails Component
 * Modal displaying detailed information about a selected location
 */

import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {translations, translateLocation} from '../utils/translations';

const LocationDetails = ({
  location,
  currentLanguage = 'en',
  onClose,
}) => {
  if (!location) return null;

  const t = translations[currentLanguage] || translations.en;
  
  const translatedName = translateLocation(location.name, 'name', currentLanguage);
  const translatedDescription = translateLocation(location.name, 'description', currentLanguage);
  
  const getTypeIcon = (type) => {
    switch (type) {
      case 'city': return '🏰';
      case 'town': return '🏘️';
      case 'dungeon': return '⚔️';
      case 'raid': return '🏛️';
      case 'flight_path': return '✈️';
      case 'landmark': return '📍';
      default: return '📍';
    }
  };

  const getFactionIcon = (faction) => {
    switch (faction.toLowerCase()) {
      case 'alliance': return '🔵';
      case 'horde': return '🔴';
      case 'neutral': return '⚪';
      default: return '⚪';
    }
  };

  const copyCoordinates = () => {
    const coordText = `[${location.coords[0]}, ${location.coords[1]}]`;
    Alert.alert(
      'Coordinates Copied',
      `Map coordinates: ${coordText}`,
      [{text: 'OK', style: 'default'}]
    );
  };

  return (
    <Modal
      visible={true}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}>
      
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <Text style={styles.typeIcon}>{getTypeIcon(location.type)}</Text>
            <View>
              <Text style={styles.locationName}>{translatedName}</Text>
              <Text style={styles.locationType}>
                {t[location.type] || location.type}
              </Text>
            </View>
          </View>
          
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          
          {/* Basic Information */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Information</Text>
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t.zone || 'Zone'}:</Text>
              <Text style={styles.infoValue}>{location.zone}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Text style={styles.infoLabel}>{t.faction || 'Faction'}:</Text>
              <View style={styles.factionContainer}>
                <Text style={styles.factionIcon}>
                  {getFactionIcon(location.faction)}
                </Text>
                <Text style={styles.infoValue}>
                  {t[location.faction.toLowerCase()] || location.faction}
                </Text>
              </View>
            </View>
            
            {location.level && (
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>{t.level || 'Level'}:</Text>
                <Text style={styles.infoValue}>{location.level}</Text>
              </View>
            )}
          </View>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{translatedDescription}</Text>
          </View>

          {/* Coordinates */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Coordinates</Text>
            <View style={styles.coordinatesContainer}>
              <View style={styles.coordinateItem}>
                <Text style={styles.coordinateLabel}>Map X:</Text>
                <Text style={styles.coordinateValue}>{location.coords[0]}</Text>
              </View>
              <View style={styles.coordinateItem}>
                <Text style={styles.coordinateLabel}>Map Y:</Text>
                <Text style={styles.coordinateValue}>{location.coords[1]}</Text>
              </View>
            </View>
            
            <TouchableOpacity
              style={styles.copyButton}
              onPress={copyCoordinates}>
              <Text style={styles.copyButtonText}>📋 Copy Coordinates</Text>
            </TouchableOpacity>
          </View>

          {/* Additional Info */}
          {location.type === 'dungeon' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Dungeon Info</Text>
              <Text style={styles.dungeonInfo}>
                Instance entrance location. Check level requirements before entering.
              </Text>
            </View>
          )}

          {location.type === 'raid' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Raid Info</Text>
              <Text style={styles.raidInfo}>
                40-player raid instance. Requires organized group and preparation.
              </Text>
            </View>
          )}

        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1b',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeIcon: {
    fontSize: 32,
    marginRight: 12,
  },
  locationName: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  locationType: {
    color: '#cccccc',
    fontSize: 14,
    textTransform: 'capitalize',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#404040',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  infoLabel: {
    color: '#cccccc',
    fontSize: 14,
    fontWeight: '600',
  },
  infoValue: {
    color: '#ffffff',
    fontSize: 14,
  },
  factionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  factionIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  description: {
    color: '#ffffff',
    fontSize: 16,
    lineHeight: 24,
  },
  coordinatesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  coordinateItem: {
    alignItems: 'center',
  },
  coordinateLabel: {
    color: '#cccccc',
    fontSize: 12,
    marginBottom: 4,
  },
  coordinateValue: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  copyButton: {
    backgroundColor: '#404040',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  copyButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  dungeonInfo: {
    color: '#FFA500',
    fontSize: 14,
    fontStyle: 'italic',
  },
  raidInfo: {
    color: '#8A2BE2',
    fontSize: 14,
    fontStyle: 'italic',
  },
});

export default LocationDetails;
