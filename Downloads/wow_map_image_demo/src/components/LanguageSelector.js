/**
 * LanguageSelector Component
 * Dropdown selector for changing app language
 */

import React from 'react';
import {View, StyleSheet} from 'react-native';
import RNPickerSelect from 'react-native-picker-select';

const LanguageSelector = ({currentLanguage, onLanguageChange}) => {
  const languages = [
    {label: 'English', value: 'en'},
    {label: '中文', value: 'zh'},
    {label: 'Español', value: 'es'},
  ];

  return (
    <View style={styles.container}>
      <RNPickerSelect
        onValueChange={onLanguageChange}
        items={languages}
        value={currentLanguage}
        style={pickerSelectStyles}
        useNativeAndroidPickerStyle={false}
        placeholder={{}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    minWidth: 100,
  },
});

const pickerSelectStyles = StyleSheet.create({
  inputIOS: {
    fontSize: 14,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#666666',
    borderRadius: 6,
    color: '#ffffff',
    backgroundColor: '#404040',
    paddingRight: 30,
  },
  inputAndroid: {
    fontSize: 14,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderWidth: 1,
    borderColor: '#666666',
    borderRadius: 6,
    color: '#ffffff',
    backgroundColor: '#404040',
    paddingRight: 30,
  },
  iconContainer: {
    top: 10,
    right: 12,
  },
});

export default LanguageSelector;
