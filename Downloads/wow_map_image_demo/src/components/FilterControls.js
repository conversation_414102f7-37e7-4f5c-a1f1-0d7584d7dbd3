/**
 * FilterControls Component
 * Toggle buttons for filtering location types
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {translations} from '../utils/translations';

const FilterControls = ({
  activeFilters,
  onFilterChange,
  currentLanguage = 'en',
}) => {
  const filterTypes = [
    {key: 'city', icon: '🏰', color: '#FFD700'},
    {key: 'town', icon: '🏘️', color: '#FFA500'},
    {key: 'dungeon', icon: '⚔️', color: '#FF4500'},
    {key: 'raid', icon: '🏛️', color: '#8A2BE2'},
    {key: 'flight_path', icon: '✈️', color: '#00BFFF'},
    {key: 'landmark', icon: '📍', color: '#32CD32'},
  ];

  const getFilterLabel = (filterKey) => {
    const t = translations[currentLanguage] || translations.en;
    return t[filterKey] || filterKey;
  };

  const toggleFilter = (filterType) => {
    onFilterChange(filterType, !activeFilters[filterType]);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Filters</Text>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.filtersContainer}>
        
        {filterTypes.map((filter) => (
          <TouchableOpacity
            key={filter.key}
            style={[
              styles.filterButton,
              {
                backgroundColor: activeFilters[filter.key]
                  ? filter.color
                  : '#404040',
                borderColor: filter.color,
              },
            ]}
            onPress={() => toggleFilter(filter.key)}
            activeOpacity={0.7}>
            
            <Text style={styles.filterIcon}>{filter.icon}</Text>
            <Text
              style={[
                styles.filterText,
                {
                  color: activeFilters[filter.key] ? '#000000' : '#ffffff',
                },
              ]}>
              {getFilterLabel(filter.key)}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  title: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingVertical: 4,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginRight: 8,
    minWidth: 80,
  },
  filterIcon: {
    fontSize: 16,
    marginRight: 6,
  },
  filterText: {
    fontSize: 12,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
});

export default FilterControls;
