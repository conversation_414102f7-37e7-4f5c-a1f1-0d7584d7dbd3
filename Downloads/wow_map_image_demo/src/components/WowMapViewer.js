/**
 * WowMapViewer Component
 * Interactive map viewer with zoom, pan, and location markers
 */

import React, {useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Image,
  TouchableOpacity,
  Text,
} from 'react-native';
import ImageZoom from 'react-native-image-pan-zoom';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

// Map dimensions (13000x12000 for high-res WoW Classic map)
const MAP_WIDTH = 13000;
const MAP_HEIGHT = 12000;

const WowMapViewer = ({
  locations = [],
  onLocationPress,
  onCoordinatePress,
  currentLanguage = 'en',
}) => {
  const [mapScale, setMapScale] = useState(1);
  const [mapPosition, setMapPosition] = useState({x: 0, y: 0});
  const imageZoomRef = useRef(null);

  // Calculate marker position relative to screen
  const getMarkerPosition = (coords) => {
    const [x, y] = coords;
    
    // Calculate the scale factor to fit map to screen
    const scaleX = screenWidth / MAP_WIDTH;
    const scaleY = (screenHeight * 0.7) / MAP_HEIGHT; // 70% of screen height for map
    const scale = Math.min(scaleX, scaleY);
    
    return {
      left: (x * scale * mapScale) + mapPosition.x,
      top: (y * scale * mapScale) + mapPosition.y,
    };
  };

  // Get marker color based on location type
  const getMarkerColor = (type) => {
    switch (type) {
      case 'city':
        return '#FFD700'; // Gold
      case 'town':
        return '#FFA500'; // Orange
      case 'dungeon':
        return '#FF4500'; // Red
      case 'raid':
        return '#8A2BE2'; // Purple
      case 'flight_path':
        return '#00BFFF'; // Blue
      case 'landmark':
        return '#32CD32'; // Green
      default:
        return '#FFFFFF'; // White
    }
  };

  // Get marker size based on location type
  const getMarkerSize = (type) => {
    switch (type) {
      case 'city':
        return 24;
      case 'town':
        return 18;
      case 'dungeon':
        return 20;
      case 'raid':
        return 26;
      case 'flight_path':
        return 16;
      case 'landmark':
        return 14;
      default:
        return 16;
    }
  };

  const handleMapPress = (event) => {
    const {locationX, locationY} = event.nativeEvent;
    
    // Convert screen coordinates to map coordinates
    const scaleX = screenWidth / MAP_WIDTH;
    const scaleY = (screenHeight * 0.7) / MAP_HEIGHT;
    const scale = Math.min(scaleX, scaleY);
    
    const mapX = (locationX - mapPosition.x) / (scale * mapScale);
    const mapY = (locationY - mapPosition.y) / (scale * mapScale);
    
    if (onCoordinatePress) {
      onCoordinatePress({x: mapX, y: mapY});
    }
  };

  const handleLocationPress = (location) => {
    if (onLocationPress) {
      onLocationPress(location);
    }
  };

  const renderMarker = (location, index) => {
    const position = getMarkerPosition(location.coords);
    const color = getMarkerColor(location.type);
    const size = getMarkerSize(location.type);

    return (
      <TouchableOpacity
        key={`marker-${index}`}
        style={[
          styles.marker,
          {
            left: position.left - size / 2,
            top: position.top - size / 2,
            width: size,
            height: size,
            backgroundColor: color,
          },
        ]}
        onPress={() => handleLocationPress(location)}
        activeOpacity={0.7}>
        <View style={styles.markerInner} />
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <ImageZoom
        ref={imageZoomRef}
        cropWidth={screenWidth}
        cropHeight={screenHeight * 0.7}
        imageWidth={screenWidth}
        imageHeight={screenHeight * 0.7}
        minScale={0.5}
        maxScale={3}
        enableSwipeDown={false}
        enableCenterFocus={false}
        onMove={(position) => {
          setMapPosition({x: position.positionX, y: position.positionY});
        }}
        onScale={(scale) => {
          setMapScale(scale);
        }}
        onClick={handleMapPress}>
        
        {/* Map Image */}
        <Image
          source={require('../../assets/wowmap.png')}
          style={styles.mapImage}
          resizeMode="contain"
        />
        
        {/* Location Markers */}
        <View style={styles.markersContainer}>
          {locations.map((location, index) => renderMarker(location, index))}
        </View>
      </ImageZoom>

      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => imageZoomRef.current?.reset()}>
          <Text style={styles.controlButtonText}>Reset View</Text>
        </TouchableOpacity>
        
        <View style={styles.scaleInfo}>
          <Text style={styles.scaleText}>
            Scale: {(mapScale * 100).toFixed(0)}%
          </Text>
        </View>
      </View>

      {/* Legend */}
      <View style={styles.legend}>
        <Text style={styles.legendTitle}>Legend</Text>
        <View style={styles.legendItems}>
          {[
            {type: 'city', label: 'Cities'},
            {type: 'town', label: 'Towns'},
            {type: 'dungeon', label: 'Dungeons'},
            {type: 'raid', label: 'Raids'},
          ].map((item) => (
            <View key={item.type} style={styles.legendItem}>
              <View
                style={[
                  styles.legendMarker,
                  {backgroundColor: getMarkerColor(item.type)},
                ]}
              />
              <Text style={styles.legendText}>{item.label}</Text>
            </View>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  markersContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  marker: {
    position: 'absolute',
    borderRadius: 50,
    borderWidth: 2,
    borderColor: '#333333',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  markerInner: {
    width: '60%',
    height: '60%',
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  controls: {
    position: 'absolute',
    top: 10,
    right: 10,
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  controlButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    marginBottom: 8,
  },
  controlButtonText: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: '600',
  },
  scaleInfo: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  scaleText: {
    color: '#ffffff',
    fontSize: 10,
  },
  legend: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 12,
    borderRadius: 8,
    minWidth: 120,
  },
  legendTitle: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  legendItems: {
    flexDirection: 'column',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  legendMarker: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#333333',
  },
  legendText: {
    color: '#ffffff',
    fontSize: 11,
  },
});

export default WowMapViewer;
