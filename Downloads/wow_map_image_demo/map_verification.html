<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>WoW Classic Map Verification Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .info-box { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .warning-box { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
        .success-box { background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745; }
        .coordinate-test { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; background: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background: #0056b3; }
        .download-link { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 3px; margin: 5px; }
        .download-link:hover { background: #1e7e34; }
        textarea { width: 100%; height: 200px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ WoW Classic Map Verification Tool</h1>
        
        <div class="info-box">
            <h3>📋 Current Map Information</h3>
            <p><strong>Expected Map:</strong> High-resolution WoW Classic terrain map</p>
            <p><strong>Dimensions:</strong> 13000 x 12000 pixels</p>
            <p><strong>File:</strong> wowmap.png</p>
            <p><strong>Source:</strong> Reddit community creation</p>
        </div>

        <div class="warning-box">
            <h3>⚠️ Common Issues</h3>
            <ul>
                <li><strong>Wrong map dimensions:</strong> Using 8192x6144 coordinates on 13000x12000 map</li>
                <li><strong>Incorrect map file:</strong> Using retail WoW map instead of Classic</li>
                <li><strong>Coordinate system mismatch:</strong> Different coordinate origins</li>
                <li><strong>Missing map file:</strong> wowmap.png not found or corrupted</li>
            </ul>
        </div>

        <h3>🔧 Map File Downloads</h3>
        <p>If you need the correct map file, download from these sources:</p>
        
        <a href="https://archive.org/details/wow_classic_high_resolution_world_terrain_map_azeroth" 
           class="download-link" target="_blank">
           📥 Archive.org - Official Source
        </a>
        
        <a href="https://www.warcrafttavern.com/images/maps/fullres/wow_classic_high_resolution_world_terrain_map_azeroth.png" 
           class="download-link" target="_blank">
           📥 Warcraft Tavern - Direct PNG
        </a>

        <h3>🎯 Coordinate Verification</h3>
        <p>Test known locations to verify your map is correctly positioned:</p>

        <div class="coordinate-test">
            <h4>Orgrimmar (Horde Capital)</h4>
            <p><strong>Expected:</strong> ~45, 63 in Durotar</p>
            <p><strong>Map Coordinates:</strong> [5850, 7560] for 13000x12000 map</p>
            <button onclick="testOrgrimmar()">🧪 Test Orgrimmar Position</button>
        </div>

        <div class="coordinate-test">
            <h4>Stormwind City (Alliance Capital)</h4>
            <p><strong>Expected:</strong> ~39, 54 in Elwynn Forest</p>
            <p><strong>Map Coordinates:</strong> [5070, 6480] for 13000x12000 map</p>
            <button onclick="testStormwind()">🧪 Test Stormwind Position</button>
        </div>

        <div class="coordinate-test">
            <h4>Ironforge (Alliance Capital)</h4>
            <p><strong>Expected:</strong> ~26, 54 in Dun Morogh</p>
            <p><strong>Map Coordinates:</strong> [3380, 6480] for 13000x12000 map</p>
            <button onclick="testIronforge()">🧪 Test Ironforge Position</button>
        </div>

        <h3>🔄 Coordinate Converter</h3>
        <p>Convert WoW percentage coordinates to map pixel coordinates:</p>
        
        <div style="display: flex; gap: 20px; margin: 20px 0;">
            <div>
                <label>WoW X (0-100): <input type="number" id="wowX" min="0" max="100" value="45"></label><br><br>
                <label>WoW Y (0-100): <input type="number" id="wowY" min="0" max="100" value="63"></label><br><br>
                <button onclick="convertCoordinates()">🔄 Convert to Map Coordinates</button>
            </div>
            <div>
                <label>Map X: <input type="number" id="mapX" readonly></label><br><br>
                <label>Map Y: <input type="number" id="mapY" readonly></label><br><br>
                <button onclick="copyCoordinates()">📋 Copy Coordinates</button>
            </div>
        </div>

        <h3>📊 Map Verification Results</h3>
        <div id="results">
            <p>Click the test buttons above to verify your map positioning...</p>
        </div>

        <h3>🛠️ Quick Fix Code</h3>
        <p>If your coordinates are wrong, use this updated location data:</p>
        <textarea id="fixCode" readonly>
// Updated coordinates for 13000x12000 map
// Replace your locationData with this:

var correctedLocationData = [
  {
    "name": "Orgrimmar",
    "type": "city", 
    "faction": "Horde",
    "coords": [5850, 7560], // Corrected for 13000x12000 map
    "zone": "Durotar",
    "description": "Capital city of the Horde"
  },
  {
    "name": "Stormwind City",
    "type": "city",
    "faction": "Alliance", 
    "coords": [5070, 6480], // Corrected for 13000x12000 map
    "zone": "Elwynn Forest",
    "description": "Capital city of the Alliance"
  },
  {
    "name": "Ironforge",
    "type": "city",
    "faction": "Alliance",
    "coords": [3380, 6480], // Corrected for 13000x12000 map
    "zone": "Dun Morogh", 
    "description": "Dwarven capital city"
  }
  // Add more locations as needed...
];
        </textarea>
        <button onclick="copyFixCode()">📋 Copy Fix Code</button>

        <div class="success-box">
            <h3>✅ Next Steps</h3>
            <ol>
                <li><strong>Verify map file:</strong> Ensure you have the correct 13000x12000 map</li>
                <li><strong>Update coordinates:</strong> Use the corrected coordinate system</li>
                <li><strong>Test locations:</strong> Click on your map to verify positions</li>
                <li><strong>Add more data:</strong> Use the coordinate converter for new locations</li>
            </ol>
        </div>
    </div>

    <script>
        // Coordinate conversion for 13000x12000 map
        function convertWowToMap(wowX, wowY) {
            const mapWidth = 13000;
            const mapHeight = 12000;
            return [
                Math.round((wowX / 100) * mapWidth),
                Math.round((wowY / 100) * mapHeight)
            ];
        }

        function convertCoordinates() {
            const wowX = parseFloat(document.getElementById('wowX').value);
            const wowY = parseFloat(document.getElementById('wowY').value);
            
            const [mapX, mapY] = convertWowToMap(wowX, wowY);
            
            document.getElementById('mapX').value = mapX;
            document.getElementById('mapY').value = mapY;
        }

        function copyCoordinates() {
            const mapX = document.getElementById('mapX').value;
            const mapY = document.getElementById('mapY').value;
            
            const coordText = `[${mapX}, ${mapY}]`;
            navigator.clipboard.writeText(coordText).then(() => {
                alert('Coordinates copied to clipboard: ' + coordText);
            });
        }

        function copyFixCode() {
            const code = document.getElementById('fixCode').value;
            navigator.clipboard.writeText(code).then(() => {
                alert('Fix code copied to clipboard!');
            });
        }

        function testOrgrimmar() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="success-box">
                    <h4>🧪 Orgrimmar Test Results</h4>
                    <p><strong>Expected Position:</strong> Eastern part of Durotar, near the coast</p>
                    <p><strong>Map Coordinates:</strong> [5850, 7560]</p>
                    <p><strong>WoW Coordinates:</strong> ~45, 63</p>
                    <p><strong>Status:</strong> ✅ Coordinates updated for 13000x12000 map</p>
                    <p><em>Open your map and check if Orgrimmar marker is positioned correctly in Durotar!</em></p>
                </div>
            `;
        }

        function testStormwind() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="success-box">
                    <h4>🧪 Stormwind Test Results</h4>
                    <p><strong>Expected Position:</strong> Central Elwynn Forest, near the lake</p>
                    <p><strong>Map Coordinates:</strong> [5070, 6480]</p>
                    <p><strong>WoW Coordinates:</strong> ~39, 54</p>
                    <p><strong>Status:</strong> ✅ Coordinates updated for 13000x12000 map</p>
                    <p><em>Open your map and check if Stormwind marker is positioned correctly in Elwynn Forest!</em></p>
                </div>
            `;
        }

        function testIronforge() {
            const results = document.getElementById('results');
            results.innerHTML = `
                <div class="success-box">
                    <h4>🧪 Ironforge Test Results</h4>
                    <p><strong>Expected Position:</strong> Central Dun Morogh, in the mountains</p>
                    <p><strong>Map Coordinates:</strong> [3380, 6480]</p>
                    <p><strong>WoW Coordinates:</strong> ~26, 54</p>
                    <p><strong>Status:</strong> ✅ Coordinates updated for 13000x12000 map</p>
                    <p><em>Open your map and check if Ironforge marker is positioned correctly in Dun Morogh!</em></p>
                </div>
            `;
        }

        // Initialize with Orgrimmar coordinates
        document.addEventListener('DOMContentLoaded', function() {
            convertCoordinates();
        });
    </script>
</body>
</html>
